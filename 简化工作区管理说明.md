# 工作区管理简化说明

## 简化前后对比

### ❌ 简化前（复杂混乱）
- 打开工作区
- 重新连接工作区  
- 保存当前到工作区
- 保存所有到工作区
- 清除内存
- 清空当前

**问题**：
- 功能重复（重新连接 = 打开工作区）
- 概念混乱（内存 vs 当前图片）
- 按钮过多，用户困惑

### ✅ 简化后（清晰简洁）
1. **打开工作区** - 选择工作文件夹
2. **保存当前** - 保存当前图片的标注到JSON文件
3. **清空当前** - 清除当前图片的所有标注框

## 三个核心功能详解

### 1. 🗂️ 打开工作区
**功能**：选择一个文件夹作为工作区
**操作**：点击按钮 → 选择文件夹 → 自动创建子文件夹
**结果**：
```
工作区/
├── 1、交付json/     # JSON输出文件夹
└── 2、原始图片/     # 图片输入文件夹
```
**快捷键**：`Ctrl+O`

### 2. 💾 保存当前
**功能**：将当前图片的标注保存为JSON文件
**前提**：需要先打开工作区，且当前图片有大题标注
**操作**：点击按钮 → 自动生成JSON文件
**结果**：在 `1、交付json/` 文件夹中生成文件
**文件名格式**：`{图片名}大题{编号}.json`
**快捷键**：`Ctrl+S`

### 3. 🗑️ 清空当前
**功能**：删除当前图片的所有标注框
**前提**：当前图片有标注
**操作**：点击按钮 → 确认对话框 → 清除所有标注
**结果**：当前图片变为空白状态，可重新标注
**注意**：此操作不可撤销

## 自动保存机制

### 🔄 后台自动保存
- **间隔**：30秒自动保存
- **对象**：有未保存更改的图片
- **状态显示**：工作区状态面板实时显示
- **无需手动操作**：在后台静默进行

### 📊 保存状态指示
- 🟢 **绿色**：所有更改已保存
- 🟡 **黄色**：有未保存更改（脉冲动画提醒）
- 🔴 **红色**：保存失败
- 🔵 **蓝色**：保存中

## 使用流程

### 标准工作流程
1. **启动应用** → 点击"打开工作区" → 选择文件夹
2. **导入图片** → 将图片放入 `2、原始图片/` 文件夹
3. **开始标注** → 使用标注工具绘制标注框
4. **编辑属性** → 在右侧面板编辑标注信息
5. **保存数据** → 点击"保存当前"或等待自动保存
6. **切换图片** → 继续标注下一张图片

### 错误处理
- **标注错误**：点击"清空当前"重新开始
- **文件夹错误**：重新点击"打开工作区"
- **保存失败**：检查文件夹权限，重试保存

## 快捷键总结

| 功能 | 快捷键 | 说明 |
|------|--------|------|
| 打开工作区 | `Ctrl+O` | 选择工作区文件夹 |
| 保存当前 | `Ctrl+S` | 保存当前图片标注 |
| 清空当前 | 无 | 需点击按钮确认 |
| 大题标注 | `1` | 选择大题工具 |
| 小题标注 | `2` | 选择小题工具 |
| 答题区域 | `3` | 选择答题区域工具 |
| 配图区域 | `4` | 选择配图区域工具 |

## 优势总结

### ✅ 简化后的优势
1. **功能清晰**：每个按钮职责明确，不重复
2. **操作简单**：只需3个按钮完成所有核心操作
3. **自动化**：后台自动保存，减少手动操作
4. **状态明确**：实时显示保存状态，心中有数
5. **容错性强**：清空功能提供重新开始的机会

### 🎯 用户体验提升
- **降低学习成本**：功能简单易懂
- **减少操作错误**：按钮少，不易误操作
- **提高工作效率**：自动保存，专注标注
- **增强数据安全**：多重保存机制

## 注意事项

1. **工作区选择**：选择空文件夹或专用文件夹作为工作区
2. **图片格式**：支持JPG、PNG、GIF、BMP、WebP格式
3. **文件权限**：确保工作区文件夹有读写权限
4. **数据备份**：重要数据建议定期备份整个工作区
5. **清空操作**：清空当前图片的标注不可撤销，请谨慎操作

通过这次简化，工作区管理变得更加直观和高效，用户可以专注于标注工作本身，而不用担心复杂的文件管理操作。

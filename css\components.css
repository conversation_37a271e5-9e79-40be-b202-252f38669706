/* OCR标注工具 - 组件样式文件 */

/* 图标字体 */

.icon-folder::before { content: "📂"; }
.icon-prev::before { content: "◀"; }
.icon-next::before { content: "▶"; }
.icon-zoom-out::before { content: "🔍-"; }
.icon-zoom-in::before { content: "🔍+"; }
.icon-zoom-reset::before { content: "🔍"; }
.icon-main-question::before { content: "📝"; }
.icon-sub-question::before { content: "📄"; }
.icon-answer-area::before { content: "✏️"; }
.icon-image-area::before { content: "🖼️"; }
.icon-clear::before { content: "🗑️"; }
.icon-save::before { content: "💾"; }
.icon-save-all::before { content: "💾📁"; }
.icon-memory::before { content: "🧠"; }
.icon-load::before { content: "📂"; }
.icon-clear-all::before { content: "🗑️"; }
.icon-export::before { content: "📤"; }
.icon-help::before { content: "❓"; }
.icon-folder::before { content: "📁"; }
.icon-refresh::before { content: "🔄"; }
.icon-visibility::before { content: "👁️"; }

/* 模态对话框 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    position: relative;
    animation: slideIn 0.3s ease;
}

.modal-close {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-md);
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: var(--text-muted);
    transition: color 0.2s ease;
}

.modal-close:hover {
    color: var(--danger-color);
}

/* 快捷键说明样式 */
.shortcuts-help {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.shortcuts-section {
    margin-bottom: 30px;
}

.shortcuts-section h3 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
}

.shortcuts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
}

.shortcut-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
    transition: all 0.2s ease;
}

.shortcut-item:hover {
    background: #e9ecef;
    transform: translateX(4px);
}

.shortcut-item kbd {
    background: #343a40;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    font-weight: bold;
    margin-right: 12px;
    min-width: 60px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.shortcut-item span {
    flex: 1;
    font-size: 14px;
    color: #495057;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .shortcuts-grid {
        grid-template-columns: 1fr;
    }

    .shortcuts-help {
        padding: 15px;
    }

    .shortcut-item {
        padding: 10px 12px;
    }
}

/* 标注区域样式 */
.annotation-item {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: var(--spacing-sm);
    cursor: pointer;
    transition: all 0.2s ease;
}

.annotation-item:hover {
    background-color: #f8f9fa;
    border-color: var(--secondary-color);
}

.annotation-item.selected {
    background-color: rgba(52, 152, 219, 0.1);
    border-color: var(--secondary-color);
}

.annotation-item .annotation-type {
    font-weight: bold;
    font-size: 12px;
    text-transform: uppercase;
    margin-bottom: var(--spacing-xs);
}

.annotation-item .annotation-content {
    font-size: 13px;
    color: var(--text-color);
    line-height: 1.4;
}

.annotation-item .annotation-coords {
    font-size: 11px;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* 标注类型颜色 */
.annotation-item.main-question .annotation-type {
    color: var(--main-question-color);
}

.annotation-item.sub-question .annotation-type {
    color: var(--sub-question-color);
}

.annotation-item.answer-area .annotation-type {
    color: var(--answer-area-color);
}

.annotation-item.image-area .annotation-type {
    color: var(--image-area-color);
}

/* 编辑表单 */
.edit-form {
    background: #f8f9fa;
    padding: var(--spacing-md);
    border-radius: 4px;
    margin-top: var(--spacing-sm);
}

.edit-form .form-row {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.edit-form .form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.edit-form .form-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
    margin-top: var(--spacing-md);
}

/* 质检模式样式 */
.quality-check-mode .left-panel {
    opacity: 0.5;
    pointer-events: none;
}

.quality-check-mode .tool-btn {
    cursor: not-allowed;
}

.quality-check-section {
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
}

.quality-info {
    font-size: 13px;
    line-height: 1.6;
}

.quality-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.quality-stat {
    text-align: center;
    padding: var(--spacing-sm);
    background: white;
    border-radius: 4px;
    border: 1px solid #b3d9ff;
}

.quality-stat .stat-number {
    font-size: 18px;
    font-weight: bold;
    color: var(--secondary-color);
}

.quality-stat .stat-label {
    font-size: 11px;
    color: var(--text-muted);
    text-transform: uppercase;
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
    margin: var(--spacing-sm) 0;
}

.progress-fill {
    height: 100%;
    background: var(--success-color);
    transition: width 0.3s ease;
}

/* 通知消息 */
.notification {
    position: fixed;
    top: calc(var(--toolbar-height) + var(--spacing-sm));
    right: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 4px;
    color: white;
    font-size: 13px;
    z-index: 1500;
    animation: slideInRight 0.3s ease;
    max-width: 300px;
}

.notification.success {
    background: var(--success-color);
}

.notification.error {
    background: var(--danger-color);
}

.notification.warning {
    background: var(--warning-color);
}

.notification.info {
    background: var(--secondary-color);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

/* 工具提示 */
.tooltip {
    position: relative;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: var(--spacing-xs);
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 滚动条样式 */
.left-panel::-webkit-scrollbar,
.right-panel::-webkit-scrollbar,
.questions-list::-webkit-scrollbar,
.images-list::-webkit-scrollbar {
    width: 6px;
}

.left-panel::-webkit-scrollbar-track,
.right-panel::-webkit-scrollbar-track,
.questions-list::-webkit-scrollbar-track,
.images-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.left-panel::-webkit-scrollbar-thumb,
.right-panel::-webkit-scrollbar-thumb,
.questions-list::-webkit-scrollbar-thumb,
.images-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.left-panel::-webkit-scrollbar-thumb:hover,
.right-panel::-webkit-scrollbar-thumb:hover,
.questions-list::-webkit-scrollbar-thumb:hover,
.images-list::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 选择状态 */
.selecting {
    cursor: crosshair !important;
}

.selecting * {
    cursor: crosshair !important;
}

/* 禁用状态 */
.disabled {
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
}

/* 高亮状态 */
.highlight {
    animation: pulse 1s ease-in-out infinite alternate;
}

@keyframes pulse {
    from { opacity: 0.7; }
    to { opacity: 1; }
}

/* 错误状态 */
.error {
    border-color: var(--danger-color) !important;
    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2) !important;
}

/* 成功状态 */
.success {
    border-color: var(--success-color) !important;
    box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.2) !important;
}

/* 工作区状态样式 */
.workspace-status {
    margin-top: 15px;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    font-size: 12px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    margin: 4px 0;
}

.status-label {
    color: #6c757d;
    font-weight: 500;
}

.status-item span:last-child {
    color: #495057;
    font-weight: 600;
}

/* 层级内容样式 */
.hierarchy-content {
    max-height: 400px;
    overflow-y: auto;
}

.hierarchy-group {
    margin-bottom: 16px;
}

.hierarchy-group-title {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin: 0 0 8px 0;
    padding: 4px 0;
    border-bottom: 1px solid #e9ecef;
}

/* 重写标注项样式以支持层级显示 */
.hierarchy-content .annotation-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.hierarchy-content .annotation-item:hover {
    background: #e9ecef;
    border-color: #dee2e6;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hierarchy-content .annotation-item.main-question {
    border-left: 4px solid #e74c3c;
}

.hierarchy-content .annotation-item.sub-question {
    border-left: 4px solid #2ecc71;
}

.hierarchy-content .annotation-item.answer-area {
    border-left: 4px solid #9b59b6;
}

.hierarchy-content .annotation-item.image-area {
    border-left: 4px solid #f39c12;
}

.hierarchy-content .annotation-type {
    font-weight: 600;
    font-size: 13px;
    color: #495057;
    margin-bottom: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hierarchy-content .annotation-content {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
    line-height: 1.4;
    max-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.hierarchy-content .annotation-coords {
    font-size: 11px;
    color: #adb5bd;
    font-family: monospace;
}

.grade-badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 500;
}

.grade-badge.correct {
    background: #d4edda;
    color: #155724;
}

.grade-badge.incorrect {
    background: #f8d7da;
    color: #721c24;
}

.grade-badge.partial {
    background: #fff3cd;
    color: #856404;
}

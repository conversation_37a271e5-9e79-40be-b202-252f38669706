# 层级显示功能使用说明

## 功能概述

OCR标注工具现已实现层级化的标注内容显示，提供更直观的标注结构管理。

## 主要特性

- ✅ 点击大题显示该大题下的所有小题和配图
- ✅ 点击小题显示该小题下的所有答题区域
- ✅ 层级内容可点击快速选择
- ✅ 批改结果状态显示
- ✅ 坐标信息显示
- ✅ 响应式设计

## 使用方法

### 1. 查看大题的子内容

1. **创建大题标注**：使用"大题"工具框选题目区域
2. **创建子内容**：在大题区域内创建小题和配图标注
3. **选择大题**：点击大题标注框
4. **查看层级内容**：在右侧面板会显示"大题X - 子内容"区域，包含：
   - **小题列表**：显示该大题下的所有小题
   - **配图列表**：显示该大题下的所有配图

### 2. 查看小题的答题区域

1. **创建小题标注**：在大题区域内使用"小题"工具框选
2. **创建答题区域**：在小题区域内使用"答题区域"工具框选
3. **选择小题**：点击小题标注框
4. **查看答题区域**：在右侧面板会显示"小题X - 答题区域"区域，包含：
   - **答题区域列表**：显示该小题下的所有答题区域
   - **批改状态**：显示每个答题区域的批改结果（正确/错误/部分正确）

### 3. 快速导航

- **点击层级内容中的项目**：可以快速选择对应的标注
- **状态指示**：
  - 绿色标记：正确
  - 红色标记：错误  
  - 黄色标记：部分正确
  - 灰色标记：未批改

## 界面说明

### 右侧面板结构

```
右侧面板
├── 选中区域信息
│   ├── 标注基本信息
│   ├── 编辑表单
│   └── OCR识别按钮（仅大题）
└── 层级内容（动态显示）
    ├── 大题选中时
    │   ├── 小题列表
    │   └── 配图列表
    └── 小题选中时
        └── 答题区域列表
```

### 层级内容显示规则

| 选中标注类型 | 显示内容 | 说明 |
|------------|---------|------|
| 大题 | 小题列表 + 配图列表 | 显示该大题下的所有子内容 |
| 小题 | 答题区域列表 | 显示该小题下的所有答题区域 |
| 答题区域 | 无 | 答题区域是最底层，无子内容 |
| 配图区域 | 无 | 配图区域是独立的，无子内容 |

## 技术实现

### 核心组件

1. **层级内容容器** (`#hierarchySection`)
   - 动态显示/隐藏
   - 根据选中标注类型切换内容

2. **事件系统**
   - `getChildAnnotations`：获取子标注数据
   - `annotationSelect`：选择标注事件

3. **数据流程**
   ```
   选中标注 → 更新层级内容 → 获取子标注 → 渲染列表 → 点击导航
   ```

### 样式特性

- **颜色编码**：不同标注类型使用不同的左边框颜色
- **状态标记**：批改结果使用颜色标记
- **交互效果**：悬停和点击效果
- **响应式**：适配不同屏幕尺寸

## 与OCR功能的集成

层级显示功能与OCR识别功能完美集成：

1. **选择大题** → 显示OCR识别按钮
2. **OCR识别** → 自动填入题干内容
3. **查看子内容** → 在层级面板中管理小题和配图
4. **批改管理** → 在答题区域列表中查看批改状态

## 使用建议

1. **标注顺序**：建议按照 大题 → 小题 → 答题区域 → 配图 的顺序进行标注
2. **层级管理**：利用层级显示功能快速导航和检查标注完整性
3. **批改跟踪**：通过答题区域列表的状态标记跟踪批改进度
4. **质量检查**：使用层级显示功能进行标注质量检查

## 注意事项

1. **父子关系**：子标注必须在父标注的区域内才能建立正确的层级关系
2. **选择状态**：只有选中标注时才会显示对应的层级内容
3. **数据同步**：层级内容会实时反映标注数据的变化
4. **性能优化**：大量标注时，层级内容会自动滚动以保持良好的用户体验

## 故障排除

### 常见问题

1. **层级内容不显示**
   - 检查是否选中了大题或小题
   - 确认标注的父子关系是否正确

2. **子标注不在列表中**
   - 检查子标注是否在父标注区域内
   - 确认标注类型是否正确

3. **点击无响应**
   - 检查浏览器控制台是否有错误
   - 确认JavaScript文件是否正确加载

### 调试方法

1. 打开浏览器开发者工具
2. 查看控制台输出
3. 检查网络请求
4. 验证DOM结构

通过层级显示功能，您可以更高效地管理复杂的标注结构，提高标注工作的效率和准确性！

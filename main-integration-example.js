// 如何将批量OCR功能集成到现有main.js的示例
// 在现有main.js文件中添加以下内容

// 1. 在文件顶部导入批量OCR集成模块
const { registerBatchOCRHandlers } = require('./batch-ocr-integration')

// 2. 在app.whenReady()中注册批量OCR处理器
app.whenReady().then(async () => {
  // 现有的初始化代码...
  const initResult = await licenseManager.initialize()
  
  if (!initResult.success) {
    console.error('许可证管理器初始化失败:', initResult.error)
  }

  // 注册批量OCR处理器（新增）
  registerBatchOCRHandlers()

  createWindow()
})

// 3. 完整的使用示例 - 在渲染进程中的实际应用

const renderProcessExample = `
// 在Vue组件或普通JS中使用批量OCR

class QuestionProcessor {
  constructor() {
    this.batchOCR = new BatchOCRManager()
    this.questionBoxes = []
    this.isProcessing = false
  }

  async initialize() {
    await this.batchOCR.initialize()
  }

  // 添加题干框
  addQuestionBox(id, canvas, type, textElement) {
    this.questionBoxes.push({
      id,
      canvas,
      type,
      textElement,
      onOCRResult: (text, confidence) => {
        console.log(\`框\${id}识别结果: \${text} (置信度: \${confidence})\`)
        // 可以在这里添加自定义处理逻辑
        this.updateBoxUI(id, text, confidence)
      }
    })
  }

  // 批量处理所有框
  async processAllBoxes(options = {}) {
    if (this.isProcessing) {
      throw new Error('正在处理中...')
    }

    try {
      this.isProcessing = true
      this.showProgressDialog()

      const result = await this.batchOCR.processQuestionBoxes(this.questionBoxes, {
        provider: options.provider || 'doubao',
        modelId: options.modelId,
        batchSize: options.batchSize || 8,
        onProgress: (progress) => {
          this.updateProgress(progress)
        }
      })

      this.hideProgressDialog()

      if (result.success) {
        this.showSuccessMessage(\`成功处理\${result.data.processedCount}个框，消耗\${result.data.totalTokens}个token\`)
        return result.data.results
      } else {
        this.showErrorMessage(result.error)
        return null
      }

    } catch (error) {
      this.hideProgressDialog()
      this.showErrorMessage(error.message)
      return null
    } finally {
      this.isProcessing = false
    }
  }

  // 处理特定类型的框
  async processBoxesByType(type, options = {}) {
    const filteredBoxes = this.questionBoxes.filter(box => box.type === type)
    
    if (filteredBoxes.length === 0) {
      throw new Error(\`没有找到类型为\${type}的框\`)
    }

    const tempProcessor = new BatchOCRManager()
    await tempProcessor.initialize()

    return await tempProcessor.processQuestionBoxes(filteredBoxes, options)
  }

  // UI更新方法
  updateBoxUI(boxId, text, confidence) {
    const box = this.questionBoxes.find(b => b.id === boxId)
    if (box && box.textElement) {
      box.textElement.value = text
      
      // 根据置信度设置样式
      if (confidence > 0.9) {
        box.textElement.className = 'ocr-high-confidence'
      } else if (confidence > 0.7) {
        box.textElement.className = 'ocr-medium-confidence'
      } else {
        box.textElement.className = 'ocr-low-confidence'
      }
    }
  }

  showProgressDialog() {
    // 显示进度对话框
    const dialog = document.getElementById('progress-dialog')
    if (dialog) {
      dialog.style.display = 'block'
    }
  }

  hideProgressDialog() {
    // 隐藏进度对话框
    const dialog = document.getElementById('progress-dialog')
    if (dialog) {
      dialog.style.display = 'none'
    }
  }

  updateProgress(progress) {
    // 更新进度条
    const progressBar = document.getElementById('progress-bar')
    const progressText = document.getElementById('progress-text')
    
    if (progressBar) {
      progressBar.style.width = \`\${progress.progress}%\`
    }
    
    if (progressText) {
      progressText.textContent = progress.message
    }
  }

  showSuccessMessage(message) {
    // 显示成功消息
    console.log('成功:', message)
    // 可以使用toast、alert或自定义通知组件
  }

  showErrorMessage(message) {
    // 显示错误消息
    console.error('错误:', message)
    // 可以使用toast、alert或自定义通知组件
  }
}

// 使用示例
async function initializeQuestionProcessor() {
  const processor = new QuestionProcessor()
  await processor.initialize()

  // 添加题干框（假设已有canvas元素）
  processor.addQuestionBox('q1', document.getElementById('canvas1'), 'question', document.getElementById('text1'))
  processor.addQuestionBox('a1', document.getElementById('canvas2'), 'answer', document.getElementById('text2'))
  processor.addQuestionBox('q2', document.getElementById('canvas3'), 'question', document.getElementById('text3'))

  // 批量处理按钮事件
  document.getElementById('batch-process-btn').addEventListener('click', async () => {
    try {
      await processor.processAllBoxes({
        provider: 'doubao',
        batchSize: 6
      })
    } catch (error) {
      console.error('批量处理失败:', error)
    }
  })

  // 只处理题干按钮事件
  document.getElementById('process-questions-btn').addEventListener('click', async () => {
    try {
      await processor.processBoxesByType('question', {
        provider: 'qwen'
      })
    } catch (error) {
      console.error('处理题干失败:', error)
    }
  })
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initializeQuestionProcessor)
`

// 4. HTML示例（进度对话框）
const htmlExample = `
<!-- 进度对话框 -->
<div id="progress-dialog" class="progress-dialog" style="display: none;">
  <div class="progress-content">
    <h3>批量OCR处理中...</h3>
    <div class="progress-bar-container">
      <div id="progress-bar" class="progress-bar"></div>
    </div>
    <p id="progress-text">准备中...</p>
  </div>
</div>

<!-- CSS样式 -->
<style>
.progress-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.progress-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  min-width: 300px;
  text-align: center;
}

.progress-bar-container {
  width: 100%;
  height: 20px;
  background: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
  margin: 10px 0;
}

.progress-bar {
  height: 100%;
  background: #4CAF50;
  transition: width 0.3s ease;
  width: 0%;
}

.ocr-high-confidence {
  border: 2px solid #4CAF50;
  background-color: #f8fff8;
}

.ocr-medium-confidence {
  border: 2px solid #FF9800;
  background-color: #fff8f0;
}

.ocr-low-confidence {
  border: 2px solid #f44336;
  background-color: #fff0f0;
}
</style>
`

console.log('批量OCR集成示例已创建')
console.log('请按照以下步骤集成:')
console.log('1. 将batch-ocr-integration.js中的registerBatchOCRHandlers()添加到main.js')
console.log('2. 将batch-ocr-preload-extension.js中的API添加到preload.js')
console.log('3. 在前端使用BatchOCRManager类处理批量OCR')
console.log('4. 根据需要调整UI和样式')

module.exports = {
  renderProcessExample,
  htmlExample
}

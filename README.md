# OCR标注工具

一个专业的图片标注系统，用于对试题图片进行精确的区域标注和文字录入，生成标准化的JSON数据用于机器学习模型训练。

## 功能特性

### 🖼️ 图片管理
- 支持导入单个或多个图片文件
- 支持导入整个文件夹的图片
- 支持常见图片格式（JPG、PNG、GIF、BMP）
- 提供图片左右翻页浏览功能
- 支持图片缩放（放大、缩小、重置）
- 支持拖拽文件导入

### 🏷️ 标注功能
- **大题标注**：标注整个大题区域
- **小题标注**：标注每个小题的题干区域
- **答题区域标注**：标注学生的答题区域
- **配图区域标注**：标注题目中的配图区域
- 维护标注的层级关系（大题→小题→答题区域）
- 支持标注区域的选择、编辑、删除
- 提供精确的坐标定位系统

### 📝 文本录入
- 支持题干内容录入
- 支持答题区域内容录入
- 支持印刷/手写属性设置
- 支持批改结果录入（正确/错误/部分正确）
- 支持正确答案和解析录入
- 自动处理答题区域引用（{答题区域N}）
- 自动处理配图引用（{图N}）

### 💾 数据管理
- 按指定格式导出JSON数据
- 支持JSON文件导入和编辑
- 一个大题生成一个JSON文件
- 支持数据验证和完整性检查
- 提供自动保存功能
- 支持批量导出功能

### 🔍 质检功能
- 提供质检模式查看标注结果
- 支持快速切换图片进行质检
- 支持标注结果的可视化显示
- 提供质检报告生成功能
- 支持返工和重新编辑

## 快速开始

### 1. 打开工具
直接在浏览器中打开 `index.html` 文件即可使用。

### 2. 导入图片
- 点击"导入图片"按钮选择图片文件
- 或点击"导入文件夹"选择整个目录
- 或直接拖拽图片文件到页面上

### 3. 开始标注
1. **标注大题**：点击"大题"按钮，在图片上拖拽框选整个大题区域
2. **标注配图**（如有）：点击"配图区域"按钮，框选题目中的配图
3. **标注小题**：点击"小题"按钮，框选每个小题的题干区域
4. **标注答题区域**：点击"答题区域"按钮，框选学生的答题区域

### 4. 填写信息
在右侧面板中填写相应的文字内容和属性信息。

### 5. 保存和导出
- 点击"保存当前"保存标注数据
- 点击"导出JSON"生成标准格式文件

## 快捷键

| 功能 | 快捷键 | 说明 |
|------|--------|------|
| 选择大题工具 | 1 | 切换到大题标注模式 |
| 选择小题工具 | 2 | 切换到小题标注模式 |
| 选择答题区域工具 | 3 | 切换到答题区域标注模式 |
| 选择配图工具 | 4 | 切换到配图标注模式 |
| 上一张图片 | ← | 切换到上一张图片 |
| 下一张图片 | → | 切换到下一张图片 |
| 放大 | + | 放大图片 |
| 缩小 | - | 缩小图片 |
| 重置缩放 | 0 | 重置到100%缩放 |
| 保存 | Ctrl + S | 保存当前标注数据 |
| 打开文件 | Ctrl + O | 打开图片文件 |
| 导出JSON | Ctrl + E | 导出当前图片的JSON |
| 切换质检模式 | Q | 在标注模式和质检模式间切换 |
| 取消操作 | Esc | 取消当前操作 |
| 删除标注 | Delete | 删除选中的标注 |

## JSON数据格式

工具生成的JSON数据格式如下：

```json
{
  "大题1": {
    "坐标": [[x1, y1], [x2, y2]],
    "题型": "填空题",
    "题干文字": "题目描述",
    "题目是否带配图": "是",
    "配图区域坐标": {
      "配图1": [[x1, y1], [x2, y2]],
      "配图2": [[x1, y1], [x2, y2]]
    },
    "小题": {
      "小题1": {
        "题干坐标": [[x1, y1], [x2, y2]],
        "题干内容": "{图1} 小题内容 {答题区域1}",
        "题干印刷手写属性": "印刷",
        "答题区域": {
          "答题区域1": {
            "答题区域坐标": [[x1, y1], [x2, y2]],
            "答题区域内容": "学生答案",
            "答题区域印刷手写属性": "手写",
            "批改结果": "正确",
            "正确答案": "标准答案",
            "答案解析": "解析内容"
          }
        }
      }
    }
  }
}
```

## 质检模式

质检模式用于验证标注结果的准确性和完整性：

1. 点击模式选择器切换到"质检模式"
2. 系统会显示当前图片的标注统计信息
3. 可以快速切换图片进行批量质检
4. 支持导出质检报告（JSON、CSV、HTML格式）

## 技术架构

- **前端技术**：HTML5 + CSS3 + JavaScript (ES6+)
- **图形处理**：Canvas API
- **数据格式**：JSON
- **存储方式**：本地存储 + 文件导出
- **部署方式**：纯前端，无需服务器

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 文件结构

```
ocr-annotation-tool/
├── index.html              # 主页面
├── test.html              # 功能测试页面
├── README.md              # 说明文档
├── css/
│   ├── styles.css         # 主样式文件
│   └── components.css     # 组件样式文件
└── js/
    ├── utils.js           # 工具函数库
    ├── coordinate-system.js # 坐标系统
    ├── image-manager.js   # 图片管理器
    ├── annotation-manager.js # 标注管理器
    ├── data-manager.js    # 数据管理器
    ├── quality-check.js   # 质检管理器
    ├── ui-manager.js      # UI管理器
    └── main.js            # 主控制器
```

## 功能测试

打开 `test.html` 可以运行功能测试，验证各个模块是否正常工作。

## 最佳实践

### 标注流程建议
1. **按顺序标注**：先大题→配图→小题→答题区域
2. **及时保存**：完成每张图片后立即保存
3. **定期备份**：每完成一批图片后导出JSON备份
4. **质检验证**：使用质检模式验证标注结果

### 效率提升技巧
1. **熟练使用快捷键**：提高操作速度
2. **合理设置缩放**：根据图片内容调整合适的缩放比例
3. **批量处理**：相似类型的图片可以复用标注模板
4. **分批处理**：避免一次处理过多图片导致性能问题

### 质量保证措施
1. **标注规范**：制定统一的标注标准
2. **交叉验证**：多人标注同一批图片进行对比
3. **定期质检**：使用质检模式定期检查标注质量
4. **数据验证**：导出前使用验证功能检查数据完整性

## 常见问题

### Q: 支持哪些图片格式？
A: 支持JPG、PNG、GIF、BMP等常见格式，推荐使用JPG格式以获得最佳性能。

### Q: 图片太大加载很慢怎么办？
A: 建议将图片压缩到5MB以内，或者使用图片压缩工具优化后再导入。

### Q: 标注框位置不准确怎么办？
A: 确保图片完全加载后再进行标注，可以使用缩放功能提高标注精度。

### Q: 如何备份标注数据？
A: 定期使用"导出JSON"功能保存数据，系统也会自动保存到本地存储。

## 更新日志

### v1.0.0 (2024-12-19)
- 初始版本发布
- 实现基础标注功能
- 支持JSON数据导入导出
- 提供质检模式
- 完整的用户界面和交互逻辑

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交问题和改进建议！

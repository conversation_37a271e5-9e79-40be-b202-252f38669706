// 前端批量OCR处理示例
// 这个文件展示如何在渲染进程中使用批量OCR功能

class BatchOCRManager {
  constructor() {
    this.isProcessing = false
    this.currentProvider = 'doubao'
    this.currentModel = null
    this.config = null
  }

  /**
   * 初始化，获取OCR配置
   */
  async initialize() {
    try {
      const result = await window.electronAPI.batchOCRGetConfig()
      if (result.success) {
        this.config = result.data
        console.log('批量OCR配置加载成功:', this.config)
        return true
      } else {
        console.error('获取OCR配置失败:', result.error)
        return false
      }
    } catch (error) {
      console.error('初始化批量OCR管理器失败:', error)
      return false
    }
  }

  /**
   * 处理题干中的多个框
   * @param {Array} questionBoxes - 题干框数组，每个包含 {id, canvas, type}
   * @param {Object} options - 处理选项
   * @returns {Promise<Object>} 处理结果
   */
  async processQuestionBoxes(questionBoxes, options = {}) {
    if (this.isProcessing) {
      throw new Error('正在处理中，请稍候...')
    }

    try {
      this.isProcessing = true
      
      // 设置处理选项
      const {
        provider = this.currentProvider,
        modelId = this.currentModel,
        batchSize = 8,
        onProgress = null
      } = options

      console.log(`开始处理${questionBoxes.length}个框，使用${provider}`)

      // 第一步：验证和准备图片数据
      if (onProgress) onProgress({ step: 'preparing', progress: 0, message: '准备图片数据...' })
      
      const imageBoxes = await this.prepareImageData(questionBoxes)
      
      if (imageBoxes.length === 0) {
        throw new Error('没有有效的图片数据')
      }

      // 第二步：验证图片数据
      if (onProgress) onProgress({ step: 'validating', progress: 20, message: '验证图片数据...' })
      
      const validation = await window.electronAPI.batchOCRValidateImages(imageBoxes)
      if (!validation.success) {
        throw new Error(`图片数据验证失败: ${validation.error}`)
      }

      if (validation.data.errorCount > 0) {
        console.warn('部分图片数据有问题:', validation.data.errors)
      }

      // 第三步：执行OCR处理
      if (onProgress) onProgress({ step: 'processing', progress: 40, message: 'OCR识别中...' })

      let result
      if (imageBoxes.length <= 10) {
        // 小批量直接处理
        result = await window.electronAPI.batchOCRProcess({
          imageBoxes: validation.data.validImages,
          provider,
          modelId
        })
      } else {
        // 大批量分批处理
        result = await window.electronAPI.batchOCRProcessLarge({
          imageBoxes: validation.data.validImages,
          provider,
          modelId,
          batchSize
        })
      }

      if (!result.success) {
        throw new Error(result.error)
      }

      // 第四步：应用结果到框
      if (onProgress) onProgress({ step: 'applying', progress: 80, message: '应用识别结果...' })
      
      const appliedResults = await this.applyResultsToBoxes(result.data.results, questionBoxes)

      if (onProgress) onProgress({ step: 'completed', progress: 100, message: '处理完成' })

      return {
        success: true,
        data: {
          processedCount: result.data.processedCount,
          totalTokens: result.data.totalTokens,
          provider: result.data.provider,
          results: appliedResults,
          batchCount: result.data.batchCount || 1
        }
      }

    } catch (error) {
      console.error('批量OCR处理失败:', error)
      return {
        success: false,
        error: error.message
      }
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * 准备图片数据
   * @param {Array} questionBoxes - 题干框数组
   * @returns {Promise<Array>} 图片数据数组
   */
  async prepareImageData(questionBoxes) {
    const imageBoxes = []

    for (const box of questionBoxes) {
      try {
        if (!box.canvas || !box.id) {
          console.warn('跳过无效的框:', box)
          continue
        }

        // 将canvas转换为base64
        const base64Image = this.canvasToBase64(box.canvas)
        
        if (base64Image) {
          imageBoxes.push({
            id: box.id,
            base64Image: base64Image,
            type: box.type || 'unknown',
            originalBox: box
          })
        }
      } catch (error) {
        console.error(`处理框${box.id}失败:`, error)
      }
    }

    return imageBoxes
  }

  /**
   * 将canvas转换为base64字符串
   * @param {HTMLCanvasElement} canvas - canvas元素
   * @returns {string} base64字符串（不包含data:image前缀）
   */
  canvasToBase64(canvas) {
    try {
      const dataURL = canvas.toDataURL('image/png')
      // 移除 "data:image/png;base64," 前缀
      return dataURL.split(',')[1]
    } catch (error) {
      console.error('Canvas转换base64失败:', error)
      return null
    }
  }

  /**
   * 将OCR结果应用到对应的框
   * @param {Array} ocrResults - OCR识别结果
   * @param {Array} originalBoxes - 原始框数组
   * @returns {Promise<Array>} 应用结果后的框数组
   */
  async applyResultsToBoxes(ocrResults, originalBoxes) {
    const appliedResults = []

    for (const result of ocrResults) {
      // 找到对应的原始框
      const originalBox = originalBoxes.find(box => box.id === result.id)
      
      if (originalBox) {
        // 应用OCR结果到框
        const appliedResult = {
          ...originalBox,
          ocrText: result.text,
          ocrConfidence: result.confidence,
          ocrError: result.error,
          hasOCRResult: true
        }

        // 如果框有文本输入元素，更新其值
        if (originalBox.textElement) {
          originalBox.textElement.value = result.text
          // 触发输入事件
          originalBox.textElement.dispatchEvent(new Event('input', { bubbles: true }))
        }

        // 如果框有回调函数，调用它
        if (originalBox.onOCRResult && typeof originalBox.onOCRResult === 'function') {
          originalBox.onOCRResult(result.text, result.confidence)
        }

        appliedResults.push(appliedResult)
      } else {
        console.warn('找不到对应的原始框:', result.id)
      }
    }

    return appliedResults
  }

  /**
   * 设置OCR提供商
   * @param {string} provider - 提供商名称
   */
  setProvider(provider) {
    if (this.config && this.config.providers.includes(provider)) {
      this.currentProvider = provider
      this.currentModel = this.config.defaultModels[provider]
      console.log(`切换到${provider}，默认模型:${this.currentModel}`)
    } else {
      console.error('不支持的OCR提供商:', provider)
    }
  }

  /**
   * 设置模型
   * @param {string} modelId - 模型ID
   */
  setModel(modelId) {
    if (this.config && this.config.models[this.currentProvider].includes(modelId)) {
      this.currentModel = modelId
      console.log(`切换模型到:${modelId}`)
    } else {
      console.error('不支持的模型:', modelId)
    }
  }

  /**
   * 获取当前配置
   */
  getCurrentConfig() {
    return {
      provider: this.currentProvider,
      model: this.currentModel,
      isProcessing: this.isProcessing,
      config: this.config
    }
  }
}

// 使用示例
async function exampleUsage() {
  // 创建批量OCR管理器
  const batchOCR = new BatchOCRManager()
  
  // 初始化
  await batchOCR.initialize()

  // 假设有一些题干框
  const questionBoxes = [
    {
      id: 'question-1',
      canvas: document.getElementById('canvas1'), // 实际的canvas元素
      type: 'question',
      textElement: document.getElementById('text1') // 对应的文本输入框
    },
    {
      id: 'answer-1',
      canvas: document.getElementById('canvas2'),
      type: 'answer',
      textElement: document.getElementById('text2')
    }
    // ... 更多框
  ]

  // 处理批量OCR
  try {
    const result = await batchOCR.processQuestionBoxes(questionBoxes, {
      provider: 'doubao',
      batchSize: 8,
      onProgress: (progress) => {
        console.log(`进度: ${progress.progress}% - ${progress.message}`)
        // 更新UI进度条
        updateProgressBar(progress.progress, progress.message)
      }
    })

    if (result.success) {
      console.log('批量OCR处理成功:', result.data)
      showSuccessMessage(`成功处理${result.data.processedCount}个框，消耗${result.data.totalTokens}个token`)
    } else {
      console.error('批量OCR处理失败:', result.error)
      showErrorMessage(result.error)
    }
  } catch (error) {
    console.error('处理异常:', error)
    showErrorMessage(error.message)
  }
}

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = BatchOCRManager
} else if (typeof window !== 'undefined') {
  window.BatchOCRManager = BatchOCRManager
}

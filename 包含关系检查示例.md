# OCR标注包含关系检查详解

## 1. 坐标标准化过程

### 输入坐标格式
```javascript
// 用户拖拽创建的坐标（可能不规范）
const coordinates = [[100, 50], [300, 200]];  // 从左上到右下
// 或者
const coordinates = [[300, 200], [100, 50]];  // 从右下到左上
```

### 标准化为矩形
```javascript
const innerRect = {
    x: Math.min(innerStart[0], innerEnd[0]),      // 最小X坐标 = 左边界
    y: Math.min(innerStart[1], innerEnd[1]),      // 最小Y坐标 = 上边界  
    width: Math.abs(innerEnd[0] - innerStart[0]), // 宽度 = |x2 - x1|
    height: Math.abs(innerEnd[1] - innerStart[1]) // 高度 = |y2 - y1|
};
```

## 2. 放宽的包含关系检查算法

### 三种关联策略（按优先级）

#### 策略1：严格包含（完全包含）
```javascript
const strictContainment = inner.x >= outer.x &&
                         inner.y >= outer.y &&
                         inner.x + inner.width <= outer.x + outer.width &&
                         inner.y + inner.height <= outer.y + outer.height;
```

#### 策略2：重叠关系（有交集且重叠比例≥30%）
```javascript
// 检查是否有重叠
const hasOverlap = !(inner.x + inner.width < outer.x ||
                   outer.x + outer.width < inner.x ||
                   inner.y + inner.height < outer.y ||
                   outer.y + outer.height < inner.y);

// 计算重叠面积比例
const overlapRatio = overlapArea / innerArea;
return overlapRatio >= 0.3; // 重叠30%以上认为有关联
```

#### 策略3：垂直对齐关系（适用于题目编号）
```javascript
// Y轴有重叠 + 水平距离在100像素内
const yOverlap = !(inner.y + inner.height < outer.y || outer.y + outer.height < inner.y);
const horizontalDistance = Math.min(距离计算...);
return yOverlap && horizontalDistance <= 100;
```

## 3. 实际示例

### 示例1：严格包含（策略1 - 返回true）
```
大题坐标: [[50, 30], [400, 300]]
小题坐标: [[100, 80], [350, 150]]

标准化后：
大题矩形: {x: 50, y: 30, width: 350, height: 270}
小题矩形: {x: 100, y: 80, width: 250, height: 70}

检查：严格包含条件全部满足
结果：true - 使用严格包含关系
```

### 示例2：重叠关系（策略2 - 返回true）
```
大题坐标: [[50, 30], [300, 200]]
小题坐标: [[250, 80], [400, 150]]

标准化后：
大题矩形: {x: 50, y: 30, width: 250, height: 170}
小题矩形: {x: 250, y: 80, width: 150, height: 70}

重叠区域: {x: 250, y: 80, width: 50, height: 70}
重叠面积: 50 × 70 = 3500
小题面积: 150 × 70 = 10500
重叠比例: 3500 ÷ 10500 = 33.3% > 30%

结果：true - 使用重叠关系（33.3%重叠）
```

### 示例3：垂直对齐（策略3 - 返回true）
```
大题坐标: [[100, 50], [400, 200]]
题号坐标: [[50, 60], [90, 90]]

标准化后：
大题矩形: {x: 100, y: 50, width: 300, height: 150}
题号矩形: {x: 50, y: 60, width: 40, height: 30}

Y轴重叠检查：
题号Y范围: [60, 90]
大题Y范围: [50, 200]
有重叠: ✓

水平距离检查：
题号右边界到大题左边界距离: |90 - 100| = 10像素 < 100像素 ✓

结果：true - 使用垂直对齐关系
```

### 示例4：无关联（返回false）
```
大题坐标: [[50, 30], [300, 200]]
小题坐标: [[400, 250], [500, 300]]

标准化后：
大题矩形: {x: 50, y: 30, width: 250, height: 170}
小题矩形: {x: 400, y: 250, width: 100, height: 50}

检查结果：
- 严格包含: ✗ (小题完全在大题外)
- 重叠关系: ✗ (无任何重叠)
- 垂直对齐: ✗ (Y轴无重叠)

结果：false - 无任何关联关系
```

## 4. 父级查找优先级

### 优先级1：选中的父级
如果用户选中了合适类型的标注，直接使用它作为父级
```javascript
if (this.selectedAnnotation && 
    this.selectedAnnotation.type === typeConfig.parent) {
    return this.selectedAnnotation.id;
}
```

### 优先级2：包含关系
遍历所有候选父级，找到第一个包含新标注的父级
```javascript
for (const parent of parentAnnotations) {
    if (this.isCoordinatesInside(coordinates, parent.coordinates)) {
        return parent.id;
    }
}
```

### 优先级3：距离最近
如果没有包含关系，选择中心点距离最近的父级
```javascript
const nearestParent = this.findNearestParent(coordinates, parentAnnotations);
return nearestParent.id;
```

## 5. 放宽策略的优势

### 支持更多实际场景
1. **跨边界小题**：小题内容可以延伸到大题框外
2. **题目编号**：编号可以在大题框左侧独立标注
3. **灵活布局**：适应各种试卷排版格式
4. **重叠检测**：部分重叠也能建立关联关系

### 智能关联算法
- **30%重叠阈值**：避免误关联，确保有意义的重叠
- **100像素距离**：适合常见的题目编号布局
- **多策略备选**：确保总能找到合适的父级

## 6. 使用建议

### 最佳实践
1. **优先选中**：选中目标父级后创建子级（最可靠）
2. **合理重叠**：确保子标注与父标注有足够重叠
3. **就近原则**：在多个候选父级时，选择距离最近的

### 特殊场景处理
1. **题目编号**：可以标注在大题框左侧，系统会自动关联
2. **跨行小题**：可以超出大题边界，只要有30%以上重叠
3. **独立答题区**：可以在小题外部，通过垂直对齐关联

### 调试信息
控制台会显示详细的关联策略信息：
- "使用严格包含关系"
- "使用重叠关系，重叠比例: XX%"
- "使用垂直对齐关系"
- "使用距离最近的父级"

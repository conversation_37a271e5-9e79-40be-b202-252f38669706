# 界面布局调整说明

## 调整内容

### 1. 左右面板宽度调整

#### 调整前
- 左侧面板：280px
- 右侧面板：320px
- 中间图片区域：剩余空间

#### 调整后
- 左侧面板：240px（减少40px）
- 右侧面板：380px（增加60px）
- 中间图片区域：增加了40px的宽度

### 2. 题干内容框高度调整

#### 调整前
- 普通textarea：最小高度80px
- 所有内容框统一高度

#### 调整后
- 普通textarea：最小高度120px（增加40px）
- 题干内容框：最小高度140px（专门优化）
- 答题区域内容：最小高度140px
- 正确答案框：最小高度140px
- 答案解析框：最小高度140px
- 配图描述框：最小高度100px

### 3. 响应式设计调整

#### 中等屏幕（≤1200px）
- 左侧面板：220px
- 右侧面板：340px

#### 小屏幕（≤768px）
- 保持原有的响应式布局

## 布局优势

### ✅ 左侧面板缩小的好处
1. **功能简化**：工作区管理已简化为3个按钮，不需要太大空间
2. **空间优化**：将更多空间让给内容编辑区域
3. **视觉平衡**：左右比例更协调

### ✅ 右侧面板扩大的好处
1. **内容显示**：长题目可以更好地显示
2. **编辑体验**：更大的编辑区域，减少滚动
3. **信息完整**：复杂标注信息可以完整显示

### ✅ 题干框加高的好处
1. **长题目支持**：数学题、语文阅读理解等长题目
2. **减少滚动**：一次性看到更多内容
3. **编辑便利**：修改长文本更方便

## 具体尺寸对比

| 元素 | 调整前 | 调整后 | 变化 |
|------|--------|--------|------|
| 左侧面板 | 280px | 240px | -40px |
| 右侧面板 | 320px | 380px | +60px |
| 普通textarea | 80px | 120px | +40px |
| 题干内容框 | 80px | 140px | +60px |
| 答题区域框 | 80px | 140px | +60px |
| 配图描述框 | 80px | 100px | +20px |

## 适用场景

### 📝 长题目场景
- 语文阅读理解题
- 数学应用题
- 英语完形填空
- 综合分析题

### 📊 复杂标注场景
- 多个小题的大题
- 详细的答案解析
- 复杂的批改说明
- 多层级的题目结构

## 用户体验提升

### 1. 视觉体验
- 更平衡的左右布局
- 更舒适的阅读区域
- 更清晰的内容层次

### 2. 操作体验
- 减少文本框滚动
- 提高编辑效率
- 降低视觉疲劳

### 3. 工作效率
- 一次性查看更多内容
- 减少界面切换
- 提高标注准确性

## 技术实现

### CSS变量调整
```css
:root {
    --left-panel-width: 240px;   /* 原280px */
    --right-panel-width: 380px;  /* 原320px */
}
```

### 特定元素高度
```css
#editContent,
#editAnswerContent,
#editCorrectAnswer,
#editAnswerExplanation {
    min-height: 140px;  /* 原80px */
}
```

### 响应式适配
```css
@media (max-width: 1200px) {
    :root {
        --left-panel-width: 220px;
        --right-panel-width: 340px;
    }
}
```

## 注意事项

1. **最小分辨率**：建议使用1366x768或更高分辨率
2. **浏览器兼容**：支持现代浏览器的CSS变量
3. **内容适配**：超长内容仍可通过滚动查看
4. **响应式**：小屏幕设备自动适配

这次调整让界面更适合处理复杂的标注任务，特别是包含长题目的教育内容标注工作。

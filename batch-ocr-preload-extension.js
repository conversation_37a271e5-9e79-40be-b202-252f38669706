// 批量OCR相关的preload扩展
// 将这些内容添加到现有的preload.js文件中

const { contextBridge, ipcRenderer } = require('electron')

// 批量OCR API
const batchOCRAPI = {
  /**
   * 批量处理OCR（小批量，一次性处理）
   * @param {Object} params - 参数对象
   * @param {Array} params.imageBoxes - 图片框数组
   * @param {string} params.provider - OCR提供商
   * @param {string} params.modelId - 模型ID
   * @returns {Promise<Object>} 处理结果
   */
  batchOCRProcess: (params) => {
    return ipcRenderer.invoke('batch-ocr-process', params)
  },

  /**
   * 大批量处理OCR（自动分批）
   * @param {Object} params - 参数对象
   * @param {Array} params.imageBoxes - 图片框数组
   * @param {string} params.provider - OCR提供商
   * @param {string} params.modelId - 模型ID
   * @param {number} params.batchSize - 每批大小
   * @returns {Promise<Object>} 处理结果
   */
  batchOCRProcessLarge: (params) => {
    return ipcRenderer.invoke('batch-ocr-process-large', params)
  },

  /**
   * 获取OCR配置信息
   * @returns {Promise<Object>} 配置信息
   */
  batchOCRGetConfig: () => {
    return ipcRenderer.invoke('batch-ocr-get-config')
  },

  /**
   * 验证图片数据格式
   * @param {Array} imageBoxes - 图片框数组
   * @returns {Promise<Object>} 验证结果
   */
  batchOCRValidateImages: (imageBoxes) => {
    return ipcRenderer.invoke('batch-ocr-validate-images', imageBoxes)
  }
}

// 将批量OCR API暴露给渲染进程
contextBridge.exposeInMainWorld('batchOCRAPI', batchOCRAPI)

// 如果已有electronAPI，则扩展它
if (typeof window !== 'undefined' && window.electronAPI) {
  // 扩展现有的electronAPI
  Object.assign(window.electronAPI, batchOCRAPI)
} else {
  // 创建新的electronAPI
  contextBridge.exposeInMainWorld('electronAPI', {
    ...batchOCRAPI,
    // 这里可以添加其他现有的API
  })
}

// 完整的preload.js示例（包含原有功能和批量OCR功能）
const completePreloadExample = `
const { contextBridge, ipcRenderer } = require('electron')

// 原有的API
const electronAPI = {
  // 文件对话框
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),

  // 许可证相关
  licenseCheckStatus: () => ipcRenderer.invoke('license-check-status'),
  licenseActivate: (licenseCode) => ipcRenderer.invoke('license-activate', licenseCode),
  licenseGetDetails: () => ipcRenderer.invoke('license-get-details'),
  licenseGenerateTrial: () => ipcRenderer.invoke('license-generate-trial'),
  licenseReset: () => ipcRenderer.invoke('license-reset'),
  licenseGetDeviceInfo: () => ipcRenderer.invoke('license-get-device-info'),
  licenseCanRun: () => ipcRenderer.invoke('license-can-run'),
  licenseSaveApiConfig: (config) => ipcRenderer.invoke('license-save-api-config', config),
  licenseLoadApiConfig: () => ipcRenderer.invoke('license-load-api-config'),
  licenseGetStorageStats: () => ipcRenderer.invoke('license-get-storage-stats'),

  // 图片保存相关
  imageSelectSaveDirectory: () => ipcRenderer.invoke('image-select-save-directory'),
  imageSaveToFile: (base64Data, filePath) => ipcRenderer.invoke('image-save-to-file', base64Data, filePath),
  imageCheckDirectoryExists: (dirPath) => ipcRenderer.invoke('image-check-directory-exists', dirPath),
  imageSaveJsonFile: (params) => ipcRenderer.invoke('image-save-json-file', params),
  imageDeleteFile: (filePath) => ipcRenderer.invoke('image-delete-file', filePath),

  // 单张OCR相关
  ocrCallDoubao: (base64Image, modelId) => ipcRenderer.invoke('ocr-call-doubao', base64Image, modelId),
  ocrCallAliyun: (base64Image, modelId) => ipcRenderer.invoke('ocr-call-aliyun', base64Image, modelId),

  // 批量OCR相关（新增）
  batchOCRProcess: (params) => ipcRenderer.invoke('batch-ocr-process', params),
  batchOCRProcessLarge: (params) => ipcRenderer.invoke('batch-ocr-process-large', params),
  batchOCRGetConfig: () => ipcRenderer.invoke('batch-ocr-get-config'),
  batchOCRValidateImages: (imageBoxes) => ipcRenderer.invoke('batch-ocr-validate-images', imageBoxes),

  // 菜单事件监听
  onMenuNewProject: (callback) => ipcRenderer.on('menu-new-project', callback),
  onMenuOpenProject: (callback) => ipcRenderer.on('menu-open-project', callback),
  onMenuSave: (callback) => ipcRenderer.on('menu-save', callback),

  // 移除监听器
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
}

contextBridge.exposeInMainWorld('electronAPI', electronAPI)
`

// 导出完整示例供参考
module.exports = {
  batchOCRAPI,
  completePreloadExample
}

# OCR标注工具详细设计文档

## 1. 项目概述

### 1.1 项目背景
OCR标注工具是一个专业的图片标注系统，用于对试题图片进行精确的区域标注和文字录入，生成标准化的JSON数据用于机器学习模型训练。该工具主要服务于教育行业的试题数字化和智能批改系统。

### 1.2 项目目标
- 提供高效、精确的图片标注功能
- 支持复杂的层级标注结构（大题→小题→答题区域→配图）
- 生成标准化的JSON数据格式
- 提供质检和返工功能
- 确保标注数据的准确性和一致性

### 1.3 技术架构
- **前端技术**：HTML5 + CSS3 + JavaScript (ES6+)
- **图形处理**：Canvas API
- **数据格式**：JSON
- **存储方式**：本地存储 + 文件导出
- **部署方式**：纯前端，无需服务器

## 2. 需求分析

### 2.1 功能性需求

#### 2.1.1 图片管理需求
- **FR-001**：支持导入单个或多个图片文件
- **FR-002**：支持导入整个文件夹的图片
- **FR-003**：支持常见图片格式（JPG、PNG、GIF、BMP）
- **FR-004**：提供图片左右翻页浏览功能
- **FR-005**：支持图片缩放（放大、缩小、重置）
- **FR-006**：支持拖拽文件导入

#### 2.1.2 标注功能需求
- **FR-007**：支持大题区域标注
- **FR-008**：支持小题区域标注
- **FR-009**：支持答题区域标注
- **FR-010**：支持配图区域标注
- **FR-011**：维护标注的层级关系（大题→小题→答题区域）
- **FR-012**：支持标注区域的选择、编辑、删除
- **FR-013**：提供精确的坐标定位系统

#### 2.1.3 文本录入需求
- **FR-014**：支持题干内容录入
- **FR-015**：支持答题区域内容录入
- **FR-016**：支持印刷/手写属性设置
- **FR-017**：支持批改结果录入（正确/错误/部分正确）
- **FR-018**：支持正确答案和解析录入
- **FR-019**：自动处理答题区域引用（{答题区域N}）
- **FR-020**：自动处理配图引用（{图N}）

#### 2.1.4 数据管理需求
- **FR-021**：按指定格式导出JSON数据
- **FR-022**：支持JSON文件导入和编辑
- **FR-023**：一个大题生成一个JSON文件
- **FR-024**：支持数据验证和完整性检查
- **FR-025**：提供自动保存功能
- **FR-026**：支持批量导出功能

#### 2.1.5 质检功能需求
- **FR-027**：提供质检模式查看标注结果
- **FR-028**：支持快速切换图片进行质检
- **FR-029**：支持标注结果的可视化显示
- **FR-030**：提供质检报告生成功能
- **FR-031**：支持返工和重新编辑

### 2.2 非功能性需求

#### 2.2.1 性能需求
- **NFR-001**：图片加载时间不超过3秒
- **NFR-002**：图片切换响应时间不超过1秒
- **NFR-003**：支持同时处理100张以内的图片
- **NFR-004**：标注操作响应时间不超过200ms
- **NFR-005**：内存使用不超过500MB

#### 2.2.2 可用性需求
- **NFR-006**：界面操作直观，新用户5分钟内上手
- **NFR-007**：提供完整的快捷键支持
- **NFR-008**：支持撤销/重做操作
- **NFR-009**：提供详细的帮助文档
- **NFR-010**：错误信息清晰易懂

#### 2.2.3 兼容性需求
- **NFR-011**：支持Chrome 80+、Firefox 75+、Safari 13+、Edge 80+
- **NFR-012**：支持Windows、macOS、Linux操作系统
- **NFR-013**：支持1920x1080及以上分辨率
- **NFR-014**：响应式设计，适配不同屏幕尺寸

#### 2.2.4 可靠性需求
- **NFR-015**：数据不丢失，提供本地存储备份
- **NFR-016**：异常情况下能够恢复工作状态
- **NFR-017**：坐标精度误差不超过1像素
- **NFR-018**：JSON格式完全符合规范

## 3. 页面设计

### 3.1 整体布局

```
┌─────────────────────────────────────────────────────────────┐
│                        顶部工具栏                            │
│  [导入] [计数] [◀上一张] [图片名称|缩放] [下一张▶] [缩放] [模式] │
├─────────────────────────────────────────────────────────────┤
│        │                                    │                │
│  左侧   │                                    │      右侧      │
│  工具   │            图片显示区域              │      信息      │
│  面板   │         (带标注画布)                │      面板      │
│        │                                    │                │
├─────────────────────────────────────────────────────────────┤
│                        底部状态栏                            │
│              [状态信息]                    [坐标显示]         │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 界面组件设计

#### 3.2.1 顶部工具栏
- **左侧区域**：
  - 导入图片按钮
  - 导入文件夹按钮
  - 图片计数显示 (当前/总数)

- **中央区域**：
  - 上一张/下一张导航按钮
  - 图片信息显示（文件名 | 缩放比例）

- **右侧区域**：
  - 缩放控制按钮（放大/缩小/重置）
  - 模式选择器（标注模式/质检模式）

#### 3.2.2 左侧工具面板
- **标注工具区**：
  - 选择大题按钮
  - 选择小题按钮
  - 选择答题区域按钮
  - 选择配图区域按钮
  - 清除选择按钮

- **大题信息区**：
  - 题型选择下拉框
  - 题干文字输入框
  - 是否带配图选择

- **快捷操作区**：
  - 保存当前按钮
  - 加载JSON按钮
  - 清空所有按钮
  - 导出JSON按钮

#### 3.2.3 中央图片显示区
- **图片容器**：
  - 支持图片缩放和平移
  - 居中显示图片
  - 响应式适配

- **标注画布**：
  - 覆盖在图片上的透明画布
  - 支持鼠标拖拽框选
  - 实时显示标注框

#### 3.2.4 右侧信息面板
- **选中区域信息**：
  - 显示当前选中标注的详细信息
  - 提供编辑表单

- **小题列表**：
  - 树形结构显示所有小题
  - 显示每个小题的答题区域
  - 支持点击选择

- **配图列表**：
  - 显示所有配图区域
  - 支持编辑配图描述

- **质检信息**：
  - 质检模式下显示详细信息
  - 提供质检操作按钮

#### 3.2.5 底部状态栏
- **左侧**：系统状态信息
- **右侧**：鼠标坐标显示

### 3.3 视觉设计规范

#### 3.3.1 色彩方案
- **主色调**：#2c3e50 (深蓝灰)
- **辅助色**：#3498db (蓝色)
- **成功色**：#27ae60 (绿色)
- **警告色**：#f39c12 (橙色)
- **危险色**：#e74c3c (红色)
- **背景色**：#f5f5f5 (浅灰)

#### 3.3.2 标注颜色
- **大题标注**：#e74c3c (红色)
- **小题标注**：#2ecc71 (绿色)
- **答题区域**：#9b59b6 (紫色)
- **配图区域**：#f39c12 (橙色)

#### 3.3.3 字体规范
- **主字体**：Microsoft YaHei, Arial, sans-serif
- **标题字体**：16px, bold
- **正文字体**：14px, normal
- **小字体**：12px, normal

#### 3.3.4 间距规范
- **大间距**：20px
- **中间距**：15px
- **小间距**：10px
- **微间距**：5px

## 4. 功能设计

### 4.1 图片管理模块

#### 4.1.1 图片导入功能
```javascript
class ImageManager {
    // 导入单个或多个图片文件
    loadImages(files) {
        // 过滤图片文件
        // 创建URL对象
        // 按文件名排序
        // 更新显示
    }
    
    // 导入文件夹
    loadFolder(files) {
        // 处理webkitdirectory文件
        // 保持目录结构信息
    }
}
```

#### 4.1.2 图片导航功能
- **翻页逻辑**：支持循环翻页或边界停止
- **预加载机制**：预加载前后图片提高响应速度
- **快捷键支持**：左右箭头键快速翻页

#### 4.1.3 图片缩放功能
```javascript
// 缩放算法
applyZoomAtPoint(mouseX, mouseY, zoomLevel) {
    // 计算鼠标相对位置
    const relativeX = (mouseX - imageLeft) / imageWidth;
    const relativeY = (mouseY - imageTop) / imageHeight;
    
    // 设置变换原点
    image.style.transformOrigin = `${relativeX * 100}% ${relativeY * 100}%`;
    image.style.transform = `scale(${zoomLevel})`;
}
```

### 4.2 标注功能模块

#### 4.2.1 坐标系统设计
```javascript
// 坐标转换核心算法
class CoordinateSystem {
    // 容器坐标 → 图片坐标
    containerToImage(containerX, containerY) {
        const relativeX = (containerX - imageLeft) / imageWidth;
        const relativeY = (containerY - imageTop) / imageHeight;
        return {
            x: relativeX * naturalWidth,
            y: relativeY * naturalHeight
        };
    }
    
    // 图片坐标 → 容器坐标
    imageToContainer(imageX, imageY) {
        const relativeX = imageX / naturalWidth;
        const relativeY = imageY / naturalHeight;
        return {
            x: relativeX * imageWidth + imageLeft,
            y: relativeY * imageHeight + imageTop
        };
    }
}
```

#### 4.2.2 标注层级管理
```javascript
// 标注数据结构
const annotationStructure = {
    mainQuestion: {
        id: "main_001",
        type: "main-question",
        coordinates: [[x1, y1], [x2, y2]],
        content: "大题题干内容",
        attributes: { printWriteAttribute: "印刷" }
    },
    subQuestions: [{
        id: "sub_001",
        type: "sub-question",
        parentId: "main_001",
        coordinates: [[x1, y1], [x2, y2]],
        content: "小题题干内容",
        attributes: { printWriteAttribute: "印刷" }
    }],
    answerAreas: [{
        id: "answer_001",
        type: "answer-area",
        parentId: "sub_001",
        areaNumber: 1,
        coordinates: [[x1, y1], [x2, y2]],
        attributes: {
            answerContent: "学生答案",
            printWriteAttribute: "手写",
            gradeResult: "正确",
            correctAnswer: "标准答案",
            answerExplanation: "解析内容"
        }
    }],
    imageAreas: [{
        id: "image_001",
        type: "image-area",
        parentId: "main_001",
        imageNumber: 1,
        coordinates: [[x1, y1], [x2, y2]],
        content: "配图描述"
    }]
};
```

#### 4.2.3 标注绘制算法
```javascript
// 标注框绘制
drawAnnotation(annotation, color) {
    const canvasCoords = this.convertToCanvasCoordinates(annotation.coordinates);
    
    // 绘制矩形框
    ctx.strokeStyle = color;
    ctx.fillStyle = color + '33'; // 半透明填充
    ctx.lineWidth = annotation.selected ? 3 : 2;
    ctx.setLineDash(annotation.selected ? [] : [5, 5]);
    
    ctx.fillRect(canvasCoords.x, canvasCoords.y, canvasCoords.width, canvasCoords.height);
    ctx.strokeRect(canvasCoords.x, canvasCoords.y, canvasCoords.width, canvasCoords.height);
    
    // 绘制标签
    this.drawLabel(annotation, canvasCoords);
}
```

### 4.3 数据管理模块

#### 4.3.1 JSON数据格式
```json
{
  "大题1": {
    "坐标": [[x1, y1], [x2, y2]],
    "题型": "填空题",
    "题干文字": "题目描述",
    "题目是否带配图": "是",
    "配图区域坐标": {
      "配图1": [[x1, y1], [x2, y2]],
      "配图2": [[x1, y1], [x2, y2]]
    },
    "小题": {
      "小题1": {
        "题干坐标": [[x1, y1], [x2, y2]],
        "题干内容": "{图1} 小题内容 {答题区域1}",
        "题干印刷手写属性": "印刷",
        "答题区域": {
          "答题区域1": {
            "答题区域坐标": [[x1, y1], [x2, y2]],
            "答题区域内容": "学生答案",
            "答题区域印刷手写属性": "手写",
            "批改结果": "正确",
            "正确答案": "标准答案",
            "答案解析": "解析内容"
          }
        }
      }
    }
  }
}
```

#### 4.3.2 数据验证规则
```javascript
// 数据验证器
class DataValidator {
    validateAnnotation(data) {
        const errors = [];
        
        // 必填字段检查
        if (!data.mainQuestion) errors.push('缺少大题标注');
        if (data.subQuestions.length === 0) errors.push('至少需要一个小题');
        
        // 坐标格式检查
        data.subQuestions.forEach((sq, index) => {
            if (!this.isValidCoordinates(sq.coordinates)) {
                errors.push(`小题${index + 1}坐标格式错误`);
            }
        });
        
        // 内容完整性检查
        data.answerAreas.forEach((aa, index) => {
            if (!aa.attributes.answerContent) {
                errors.push(`答题区域${aa.areaNumber}内容为空`);
            }
        });
        
        return { valid: errors.length === 0, errors };
    }
}
```

### 4.4 质检功能模块

#### 4.4.1 质检模式设计
```javascript
class QualityCheckManager {
    // 进入质检模式
    enterQualityCheckMode() {
        // 隐藏编辑界面
        // 显示只读标注
        // 加载质检数据
        // 禁用标注功能
    }
    
    // 质检数据显示
    displayQualityCheckData(jsonData) {
        // 解析JSON数据
        // 生成可视化界面
        // 显示标注统计信息
        // 提供快速导航
    }
}
```

#### 4.4.2 批量质检功能
```javascript
// 批量质检流程
startBatchQualityCheck() {
    // 加载所有图片和对应JSON
    // 创建质检任务队列
    // 提供快速切换界面
    // 记录质检状态
    // 生成质检报告
}
```

## 5. 使用流程设计

### 5.1 标准标注流程

#### 5.1.1 准备阶段
1. **启动工具**：在浏览器中打开index.html
2. **导入图片**：
   - 点击"导入图片"选择单个或多个文件
   - 或点击"导入文件夹"选择整个目录
   - 或直接拖拽文件到页面上
3. **图片预览**：查看导入的图片列表和当前图片

#### 5.1.2 大题标注
1. **选择工具**：点击"选择大题"按钮或按键盘"1"
2. **框选区域**：在图片上拖拽鼠标框选整个大题区域
3. **填写信息**：
   - 在左侧面板选择题型
   - 输入题干文字
   - 选择是否带配图
4. **确认标注**：标注框显示为红色，表示大题区域

#### 5.1.3 配图标注（如有）
1. **选择工具**：点击"选择配图区域"按钮或按键盘"4"
2. **框选配图**：框选题目中的配图区域
3. **编辑描述**：在右侧面板编辑配图描述
4. **确认标注**：配图区域显示为橙色

#### 5.1.4 小题标注
1. **选择工具**：点击"选择小题"按钮或按键盘"2"
2. **框选区域**：框选每个小题的题干区域
3. **填写内容**：
   - 在右侧面板输入小题题干内容
   - 选择印刷/手写属性
   - 系统自动添加配图引用{图N}
4. **重复操作**：为每个小题重复此过程

#### 5.1.5 答题区域标注
1. **选择工具**：点击"选择答题区域"按钮或按键盘"3"
2. **框选区域**：框选学生的答题区域
3. **填写信息**：
   - 答题区域内容（学生的答案）
   - 印刷/手写属性
   - 批改结果（正确/错误/部分正确）
   - 正确答案
   - 答案解析
4. **自动引用**：系统自动在题干中添加{答题区域N}引用

#### 5.1.6 完成标注
1. **检查完整性**：确保所有必要区域都已标注
2. **保存数据**：点击"保存当前"保存到本地存储
3. **导出JSON**：点击"导出JSON"生成标准格式文件
4. **继续下一张**：使用导航按钮切换到下一张图片

### 5.2 质检流程

#### 5.2.1 进入质检模式
1. **切换模式**：在顶部工具栏选择"质检模式"
2. **加载数据**：
   - 自动加载当前图片的标注数据
   - 或手动加载JSON文件进行质检
3. **界面变化**：
   - 左侧工具面板隐藏
   - 右侧显示质检信息
   - 标注区域变为只读模式

#### 5.2.2 质检操作
1. **查看标注**：
   - 所有标注区域以不同颜色显示
   - 右侧面板显示详细信息
   - 可以快速切换图片
2. **验证数据**：
   - 检查标注位置是否准确
   - 验证文字内容是否正确
   - 确认引用关系是否合理
3. **问题处理**：
   - 发现问题时点击"编辑标注"返回标注模式
   - 修改完成后重新进入质检模式验证

#### 5.2.3 批量质检
1. **启动批量质检**：选择批量质检模式
2. **快速浏览**：
   - 使用快捷键快速切换图片
   - 标记已检查和有问题的图片
3. **生成报告**：完成后生成质检报告

### 5.3 数据管理流程

#### 5.3.1 数据保存
1. **自动保存**：系统每30秒自动保存到本地存储
2. **手动保存**：点击"保存当前"或使用Ctrl+S
3. **数据恢复**：页面刷新后自动恢复未保存的数据

#### 5.3.2 数据导出
1. **单个导出**：点击"导出JSON"导出当前图片数据
2. **批量导出**：在数据管理面板选择批量导出
3. **文件命名**：自动按"图片名大题1.json"格式命名

#### 5.3.3 数据导入
1. **加载JSON**：点击"加载JSON"选择已有的JSON文件
2. **数据验证**：系统自动验证JSON格式和完整性
3. **数据应用**：验证通过后应用到当前图片

### 5.4 异常处理流程

#### 5.4.1 数据丢失处理
1. **检测机制**：页面加载时检查本地存储
2. **恢复提示**：发现未保存数据时提示用户恢复
3. **备份机制**：定期创建数据备份

#### 5.4.2 错误处理
1. **输入验证**：实时验证用户输入
2. **错误提示**：清晰的错误信息和解决建议
3. **容错机制**：部分数据错误不影响整体功能

#### 5.4.3 性能优化
1. **图片优化**：大图片自动压缩显示
2. **内存管理**：及时释放不用的图片资源
3. **响应优化**：异步处理耗时操作

## 6. 技术实现要点

### 6.1 关键算法

#### 6.1.1 坐标转换算法
- 使用相对坐标(0-1)作为中间值提高精度
- 支持任意缩放级别下的精确转换
- 处理图片居中和缩放的复杂情况

#### 6.1.2 碰撞检测算法
- 点与矩形的碰撞检测
- 矩形与矩形的重叠检测
- 支持标注区域的精确选择

#### 6.1.3 缩放算法
- 以指定点为中心的缩放变换
- 保持标注位置的相对准确性
- 流畅的缩放动画效果

### 6.2 性能优化策略

#### 6.2.1 图片处理优化
- 图片预加载机制
- Canvas绘制优化
- 内存使用控制

#### 6.2.2 事件处理优化
- 事件委托减少监听器数量
- 防抖和节流优化频繁操作
- 异步处理避免界面卡顿

#### 6.2.3 数据处理优化
- JSON数据的增量更新
- 本地存储的压缩和优化
- 大量数据的分页处理

### 6.3 兼容性处理

#### 6.3.1 浏览器兼容性
- 使用标准Web API
- 提供降级方案
- 特性检测和polyfill

#### 6.3.2 设备兼容性
- 响应式设计适配不同屏幕
- 触摸设备的手势支持
- 高DPI屏幕的适配

## 7. 测试策略

### 7.1 功能测试
- 图片导入和显示测试
- 标注功能完整性测试
- 数据导出和导入测试
- 质检功能测试

### 7.2 性能测试
- 大量图片处理性能测试
- 内存使用情况测试
- 响应时间测试

### 7.3 兼容性测试
- 多浏览器兼容性测试
- 不同操作系统测试
- 不同分辨率适配测试

### 7.4 用户体验测试
- 易用性测试
- 界面友好性测试
- 错误处理测试

## 8. 部署和维护

### 8.1 部署方案
- 纯前端部署，无需服务器
- 支持本地文件系统运行
- 可打包为桌面应用

### 8.2 维护策略
- 定期更新和bug修复
- 用户反馈收集和处理
- 功能迭代和优化

### 8.3 文档维护
- 用户手册更新
- 技术文档维护
- 版本更新日志

## 9. 用户操作指南

### 9.1 快捷键一览表

| 功能 | 快捷键 | 说明 |
|------|--------|------|
| 选择大题工具 | 1 | 切换到大题标注模式 |
| 选择小题工具 | 2 | 切换到小题标注模式 |
| 选择答题区域工具 | 3 | 切换到答题区域标注模式 |
| 选择配图工具 | 4 | 切换到配图标注模式 |
| 上一张图片 | ← | 切换到上一张图片 |
| 下一张图片 | → | 切换到下一张图片 |
| 放大 | + | 放大图片 |
| 缩小 | - | 缩小图片 |
| 重置缩放 | 0 | 重置到100%缩放 |
| 以鼠标位置缩放 | Ctrl + 滚轮 | 以鼠标位置为中心缩放 |
| 保存 | Ctrl + S | 保存当前标注数据 |
| 打开文件 | Ctrl + O | 打开图片文件 |
| 导出JSON | Ctrl + E | 导出当前图片的JSON |
| 切换质检模式 | Q | 在标注模式和质检模式间切换 |
| 显示帮助 | F1 | 显示帮助信息 |
| 全屏模式 | F2 | 切换全屏显示 |
| 取消操作 | Esc | 取消当前操作 |
| 删除标注 | Delete | 删除选中的标注 |

### 9.2 常见问题解答

#### 9.2.1 图片相关问题

**Q: 支持哪些图片格式？**
A: 支持JPG、PNG、GIF、BMP等常见格式，推荐使用JPG格式以获得最佳性能。

**Q: 图片太大加载很慢怎么办？**
A: 建议将图片压缩到5MB以内，或者使用图片压缩工具优化后再导入。

**Q: 如何批量导入图片？**
A: 可以使用"导入文件夹"功能，或者选择多个文件进行批量导入。

#### 9.2.2 标注相关问题

**Q: 标注框位置不准确怎么办？**
A: 确保图片完全加载后再进行标注，可以使用"测试坐标"功能验证坐标系统。

**Q: 如何修改已有的标注？**
A: 点击标注区域选中后，在右侧面板直接编辑相关信息。

**Q: 答题区域引用是如何工作的？**
A: 系统会自动在小题题干中插入{答题区域N}引用，确保数据关联正确。

#### 9.2.3 数据相关问题

**Q: JSON文件格式有什么要求？**
A: 必须严格按照规定格式，包含所有必需字段，坐标格式为[[x1,y1],[x2,y2]]。

**Q: 如何备份标注数据？**
A: 定期使用"导出JSON"功能保存数据，系统也会自动保存到本地存储。

**Q: 数据丢失了怎么办？**
A: 检查本地存储是否有自动保存的数据，或者从最近的JSON备份恢复。

### 9.3 最佳实践建议

#### 9.3.1 标注流程建议
1. **按顺序标注**：先大题→配图→小题→答题区域
2. **及时保存**：完成每张图片后立即保存
3. **定期备份**：每完成一批图片后导出JSON备份
4. **质检验证**：使用质检模式验证标注结果

#### 9.3.2 效率提升技巧
1. **熟练使用快捷键**：提高操作速度
2. **合理设置缩放**：根据图片内容调整合适的缩放比例
3. **批量处理**：相似类型的图片可以复用标注模板
4. **分批处理**：避免一次处理过多图片导致性能问题

#### 9.3.3 质量保证措施
1. **标注规范**：制定统一的标注标准
2. **交叉验证**：多人标注同一批图片进行对比
3. **定期质检**：使用质检模式定期检查标注质量
4. **数据验证**：导出前使用验证功能检查数据完整性

## 10. 系统架构详解

### 10.1 模块依赖关系

```
main.js (主控制器)
├── image-manager.js (图片管理)
├── annotation.js (标注功能)
├── data-manager.js (数据管理)
└── quality-check.js (质检功能)
```

### 10.2 数据流向图

```
用户操作 → 事件处理 → 坐标转换 → 数据更新 → 界面刷新
    ↓           ↓           ↓           ↓           ↓
  鼠标点击   → 画布事件   → 图片坐标   → 标注数据   → 重绘标注
  键盘输入   → 表单事件   → 文本内容   → 属性更新   → 列表更新
  文件拖拽   → 文件事件   → 图片加载   → 图片列表   → 显示更新
```

### 10.3 核心类设计

#### 10.3.1 ImageManager类
```javascript
class ImageManager {
    constructor() {
        this.images = [];           // 图片列表
        this.currentIndex = 0;      // 当前图片索引
        this.zoomLevel = 1;         // 缩放级别
    }

    // 核心方法
    loadImages(files)              // 加载图片
    updateCanvas()                 // 更新画布
    getImageCoordinates(x, y)      // 坐标转换
    applyZoomAtPoint(x, y, zoom)   // 缩放应用
}
```

#### 10.3.2 AnnotationManager类
```javascript
class AnnotationManager {
    constructor() {
        this.annotations = {};      // 标注数据
        this.currentTool = '';      // 当前工具
        this.selectedAnnotation = null; // 选中标注
    }

    // 核心方法
    createAnnotation(coords)       // 创建标注
    convertToImageCoordinates()    // 坐标转换
    redrawAnnotations()           // 重绘标注
    selectAnnotation(annotation)   // 选择标注
}
```

### 10.4 性能监控指标

#### 10.4.1 关键性能指标
- **图片加载时间**：< 3秒
- **标注响应时间**：< 200ms
- **图片切换时间**：< 1秒
- **内存使用量**：< 500MB
- **坐标转换精度**：± 1像素

#### 10.4.2 性能优化措施
- 图片预加载和缓存
- Canvas绘制优化
- 事件处理防抖
- 内存泄漏防护
- 异步操作处理

## 11. 扩展功能设计

### 11.1 未来功能规划

#### 11.1.1 短期规划（v3.0）
- **批量标注模板**：保存和应用标注模板
- **协作功能**：多人同时标注支持
- **数据统计**：标注进度和质量统计
- **自定义快捷键**：用户自定义操作快捷键

#### 11.1.2 中期规划（v4.0）
- **AI辅助标注**：智能识别和建议标注区域
- **云端同步**：数据云端存储和同步
- **移动端支持**：平板和手机端适配
- **插件系统**：支持第三方功能扩展

#### 11.1.3 长期规划（v5.0）
- **实时协作**：多人实时协作标注
- **版本控制**：标注数据的版本管理
- **API接口**：提供标准化API接口
- **企业级功能**：权限管理、审批流程等

### 11.2 技术演进路线

#### 11.2.1 架构升级
- 从单体应用向微服务架构演进
- 引入现代前端框架（React/Vue）
- 采用TypeScript提高代码质量
- 集成自动化测试和CI/CD

#### 11.2.2 技术栈升级
- WebAssembly提升计算性能
- WebGL加速图形渲染
- Service Worker支持离线使用
- PWA技术提升用户体验

---

**文档版本**：v1.0
**创建日期**：2024-12-19
**最后更新**：2024-12-19
**文档状态**：正式版
**文档作者**：OCR标注工具开发团队

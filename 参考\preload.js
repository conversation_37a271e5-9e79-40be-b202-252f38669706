const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 通用invoke方法
  invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),

  // 文件对话框
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),

  // 菜单事件监听
  onMenuNewProject: (callback) => ipcRenderer.on('menu-new-project', callback),
  onMenuOpenProject: (callback) => ipcRenderer.on('menu-open-project', callback),
  onMenuSave: (callback) => ipcRenderer.on('menu-save', callback),

  // 许可证相关API
  license: {
    checkStatus: () => ipcRenderer.invoke('license-check-status'),
    activate: (licenseCode) => ipcRenderer.invoke('license-activate', licenseCode),
    getDetails: () => ipcRenderer.invoke('license-get-details'),
    generateTrial: () => ipcRenderer.invoke('license-generate-trial'),
    reset: () => ipcRenderer.invoke('license-reset'),
    getDeviceInfo: () => ipcRenderer.invoke('license-get-device-info'),
    canRun: () => ipcRenderer.invoke('license-can-run'),
    saveApiConfig: (config) => ipcRenderer.invoke('license-save-api-config', config),
    loadApiConfig: () => ipcRenderer.invoke('license-load-api-config'),
    getStorageStats: () => ipcRenderer.invoke('license-get-storage-stats')
  },

  // 图片保存相关API
  image: {
    selectSaveDirectory: () => ipcRenderer.invoke('image-select-save-directory'),
    saveToFile: (base64Data, filePath) => ipcRenderer.invoke('image-save-to-file', base64Data, filePath),
    checkDirectoryExists: (dirPath) => ipcRenderer.invoke('image-check-directory-exists', dirPath),
    saveJsonFile: (options) => ipcRenderer.invoke('image-save-json-file', options),
    deleteFile: (filePath) => ipcRenderer.invoke('image-delete-file', filePath)
  },

  // OCR相关API
  ocr: {
    callDoubaoOCR: (base64Image, modelId) => ipcRenderer.invoke('ocr-call-doubao', base64Image, modelId),
    callAliyunOCR: (base64Image, modelId) => ipcRenderer.invoke('ocr-call-aliyun', base64Image, modelId)
  },

  // 移除监听器
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
})

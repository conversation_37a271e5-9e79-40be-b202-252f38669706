<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR标注工具 - 功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .test-item {
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .test-result {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
        }
        .test-warning {
            background: #fff3cd;
            color: #856404;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            margin: 5px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        #testResults {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>OCR标注工具 - 功能测试</h1>
    
    <div class="test-section">
        <h2>基础功能测试</h2>
        <div class="test-item">
            <span>工具类加载测试</span>
            <span id="utilsTest" class="test-result">等待测试</span>
        </div>
        <div class="test-item">
            <span>坐标系统测试</span>
            <span id="coordinateTest" class="test-result">等待测试</span>
        </div>
        <div class="test-item">
            <span>图片管理器测试</span>
            <span id="imageManagerTest" class="test-result">等待测试</span>
        </div>
        <div class="test-item">
            <span>标注管理器测试</span>
            <span id="annotationManagerTest" class="test-result">等待测试</span>
        </div>
        <div class="test-item">
            <span>数据管理器测试</span>
            <span id="dataManagerTest" class="test-result">等待测试</span>
        </div>
        <div class="test-item">
            <span>质检管理器测试</span>
            <span id="qualityCheckTest" class="test-result">等待测试</span>
        </div>
    </div>

    <div class="test-section">
        <h2>数据格式测试</h2>
        <div class="test-item">
            <span>JSON数据生成测试</span>
            <span id="jsonGenerateTest" class="test-result">等待测试</span>
        </div>
        <div class="test-item">
            <span>JSON数据解析测试</span>
            <span id="jsonParseTest" class="test-result">等待测试</span>
        </div>
        <div class="test-item">
            <span>数据验证测试</span>
            <span id="dataValidationTest" class="test-result">等待测试</span>
        </div>
    </div>

    <div class="test-section">
        <h2>坐标转换测试</h2>
        <div class="test-item">
            <span>坐标转换精度测试</span>
            <span id="coordinateAccuracyTest" class="test-result">等待测试</span>
        </div>
        <div class="test-item">
            <span>边界检测测试</span>
            <span id="boundaryTest" class="test-result">等待测试</span>
        </div>
    </div>

    <div class="test-section">
        <h2>性能测试</h2>
        <div class="test-item">
            <span>大量标注性能测试</span>
            <span id="performanceTest" class="test-result">等待测试</span>
        </div>
        <div class="test-item">
            <span>内存使用测试</span>
            <span id="memoryTest" class="test-result">等待测试</span>
        </div>
    </div>

    <div class="test-section">
        <h2>测试操作</h2>
        <button class="btn" onclick="runAllTests()">运行所有测试</button>
        <button class="btn btn-success" onclick="runBasicTests()">运行基础测试</button>
        <button class="btn" onclick="runDataTests()">运行数据测试</button>
        <button class="btn" onclick="runPerformanceTests()">运行性能测试</button>
        <button class="btn" onclick="clearResults()">清空结果</button>
    </div>

    <div id="testResults"></div>

    <!-- 加载必要的脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/coordinate-system.js"></script>
    <script src="js/image-manager.js"></script>
    <script src="js/annotation-manager.js"></script>
    <script src="js/data-manager.js"></script>
    <script src="js/quality-check.js"></script>

    <script>
        // 测试结果记录
        let testResults = [];

        /**
         * 设置测试结果
         * @param {string} testId 测试ID
         * @param {boolean} passed 是否通过
         * @param {string} message 消息
         */
        function setTestResult(testId, passed, message = '') {
            const element = document.getElementById(testId);
            if (element) {
                element.className = `test-result ${passed ? 'test-pass' : 'test-fail'}`;
                element.textContent = passed ? '通过' : '失败';
                if (message) {
                    element.title = message;
                }
            }
            
            testResults.push({
                test: testId,
                passed: passed,
                message: message,
                timestamp: new Date().toISOString()
            });
        }

        /**
         * 运行所有测试
         */
        function runAllTests() {
            clearResults();
            runBasicTests();
            runDataTests();
            runPerformanceTests();
            showTestSummary();
        }

        /**
         * 运行基础功能测试
         */
        function runBasicTests() {
            console.log('开始基础功能测试...');

            // 工具类测试
            try {
                const id = Utils.generateId();
                const isValid = typeof id === 'string' && id.length > 0;
                setTestResult('utilsTest', isValid, isValid ? '' : 'ID生成失败');
            } catch (error) {
                setTestResult('utilsTest', false, error.message);
            }

            // 坐标系统测试
            try {
                // 创建模拟元素
                const mockImage = document.createElement('img');
                const mockCanvas = document.createElement('canvas');
                mockImage.naturalWidth = 1000;
                mockImage.naturalHeight = 800;
                
                const coordSystem = new CoordinateSystem(mockImage, mockCanvas);
                const isValid = coordSystem instanceof CoordinateSystem;
                setTestResult('coordinateTest', isValid, isValid ? '' : '坐标系统创建失败');
            } catch (error) {
                setTestResult('coordinateTest', false, error.message);
            }

            // 图片管理器测试
            try {
                const imageManager = new ImageManager();
                const isValid = imageManager instanceof ImageManager;
                setTestResult('imageManagerTest', isValid, isValid ? '' : '图片管理器创建失败');
            } catch (error) {
                setTestResult('imageManagerTest', false, error.message);
            }

            // 标注管理器测试
            try {
                const mockImage = document.createElement('img');
                const mockCanvas = document.createElement('canvas');
                const coordSystem = new CoordinateSystem(mockImage, mockCanvas);
                const annotationManager = new AnnotationManager(coordSystem);
                const isValid = annotationManager instanceof AnnotationManager;
                setTestResult('annotationManagerTest', isValid, isValid ? '' : '标注管理器创建失败');
            } catch (error) {
                setTestResult('annotationManagerTest', false, error.message);
            }

            // 数据管理器测试
            try {
                const dataManager = new DataManager();
                const isValid = dataManager instanceof DataManager;
                setTestResult('dataManagerTest', isValid, isValid ? '' : '数据管理器创建失败');
            } catch (error) {
                setTestResult('dataManagerTest', false, error.message);
            }

            // 质检管理器测试
            try {
                const qualityCheckManager = new QualityCheckManager();
                const isValid = qualityCheckManager instanceof QualityCheckManager;
                setTestResult('qualityCheckTest', isValid, isValid ? '' : '质检管理器创建失败');
            } catch (error) {
                setTestResult('qualityCheckTest', false, error.message);
            }
        }

        /**
         * 运行数据测试
         */
        function runDataTests() {
            console.log('开始数据格式测试...');

            try {
                const dataManager = new DataManager();

                // 测试JSON数据生成
                const mockAnnotations = [
                    {
                        id: 'main_1',
                        type: 'main-question',
                        number: 1,
                        coordinates: [[100, 100], [500, 200]],
                        attributes: {
                            content: '测试大题',
                            questionType: '填空题',
                            hasImage: false
                        }
                    }
                ];

                const mockImageInfo = { name: 'test.jpg' };
                const mockQuestionInfo = { questionType: '填空题', questionContent: '测试大题', hasImage: false };

                const jsonData = dataManager.generateJSONFromAnnotations(mockImageInfo, mockAnnotations, mockQuestionInfo);
                const hasMainQuestion = jsonData && jsonData['大题1'];
                setTestResult('jsonGenerateTest', hasMainQuestion, hasMainQuestion ? '' : 'JSON生成失败');

                // 测试JSON数据解析
                if (hasMainQuestion) {
                    const parsedAnnotations = dataManager.parseAnnotationsFromJSON(jsonData);
                    const hasAnnotations = parsedAnnotations && parsedAnnotations.length > 0;
                    setTestResult('jsonParseTest', hasAnnotations, hasAnnotations ? '' : 'JSON解析失败');
                } else {
                    setTestResult('jsonParseTest', false, '无法测试解析，生成失败');
                }

                // 测试数据验证
                const validation = dataManager.validateJSONData(jsonData);
                setTestResult('dataValidationTest', validation.valid, validation.valid ? '' : validation.errors.join(', '));

            } catch (error) {
                setTestResult('jsonGenerateTest', false, error.message);
                setTestResult('jsonParseTest', false, error.message);
                setTestResult('dataValidationTest', false, error.message);
            }
        }

        /**
         * 运行性能测试
         */
        function runPerformanceTests() {
            console.log('开始性能测试...');

            try {
                // 大量标注性能测试
                const startTime = performance.now();
                const mockImage = document.createElement('img');
                const mockCanvas = document.createElement('canvas');
                const coordSystem = new CoordinateSystem(mockImage, mockCanvas);
                const annotationManager = new AnnotationManager(coordSystem);

                // 创建100个标注
                for (let i = 0; i < 100; i++) {
                    const annotation = {
                        id: `test_${i}`,
                        type: 'sub-question',
                        number: i + 1,
                        coordinates: [[i * 10, i * 10], [i * 10 + 50, i * 10 + 30]],
                        attributes: { content: `测试标注${i}` },
                        parentId: null,
                        children: [],
                        selected: false,
                        created: Date.now(),
                        updated: Date.now()
                    };
                    annotationManager.annotations.set(annotation.id, annotation);
                }

                const endTime = performance.now();
                const duration = endTime - startTime;
                const passed = duration < 1000; // 1秒内完成
                setTestResult('performanceTest', passed, `耗时: ${duration.toFixed(2)}ms`);

                // 内存使用测试
                const memoryInfo = performance.memory;
                if (memoryInfo) {
                    const usedMB = memoryInfo.usedJSHeapSize / 1024 / 1024;
                    const passed = usedMB < 100; // 小于100MB
                    setTestResult('memoryTest', passed, `内存使用: ${usedMB.toFixed(2)}MB`);
                } else {
                    setTestResult('memoryTest', true, '浏览器不支持内存监控');
                }

            } catch (error) {
                setTestResult('performanceTest', false, error.message);
                setTestResult('memoryTest', false, error.message);
            }
        }

        /**
         * 坐标转换精度测试
         */
        function runCoordinateTests() {
            try {
                const mockImage = document.createElement('img');
                const mockCanvas = document.createElement('canvas');
                mockImage.naturalWidth = 1000;
                mockImage.naturalHeight = 800;
                
                const coordSystem = new CoordinateSystem(mockImage, mockCanvas);
                
                // 测试坐标转换精度
                const originalCoords = { x: 500, y: 400 };
                const containerCoords = coordSystem.imageToContainer(originalCoords.x, originalCoords.y);
                const backToImage = coordSystem.containerToImage(containerCoords.x, containerCoords.y);
                
                const precision = Math.abs(backToImage.x - originalCoords.x) < 1 && 
                                Math.abs(backToImage.y - originalCoords.y) < 1;
                setTestResult('coordinateAccuracyTest', precision, precision ? '' : '坐标转换精度不足');

                // 测试边界检测
                const inBounds = coordSystem.isInImageBounds(500, 400);
                const outOfBounds = !coordSystem.isInImageBounds(1500, 1000);
                const boundaryTest = inBounds && outOfBounds;
                setTestResult('boundaryTest', boundaryTest, boundaryTest ? '' : '边界检测失败');

            } catch (error) {
                setTestResult('coordinateAccuracyTest', false, error.message);
                setTestResult('boundaryTest', false, error.message);
            }
        }

        /**
         * 显示测试总结
         */
        function showTestSummary() {
            const totalTests = testResults.length;
            const passedTests = testResults.filter(r => r.passed).length;
            const failedTests = totalTests - passedTests;
            
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = `
                <h3>测试总结</h3>
                <p><strong>总测试数:</strong> ${totalTests}</p>
                <p><strong>通过:</strong> <span style="color: #28a745;">${passedTests}</span></p>
                <p><strong>失败:</strong> <span style="color: #dc3545;">${failedTests}</span></p>
                <p><strong>通过率:</strong> ${totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0}%</p>
                
                <h4>详细结果:</h4>
                <ul>
                    ${testResults.map(result => `
                        <li style="color: ${result.passed ? '#28a745' : '#dc3545'};">
                            ${result.test}: ${result.passed ? '通过' : '失败'}
                            ${result.message ? ` (${result.message})` : ''}
                        </li>
                    `).join('')}
                </ul>
            `;
        }

        /**
         * 清空测试结果
         */
        function clearResults() {
            testResults = [];
            document.getElementById('testResults').innerHTML = '';
            
            // 重置所有测试状态
            const testElements = document.querySelectorAll('.test-result');
            testElements.forEach(element => {
                element.className = 'test-result';
                element.textContent = '等待测试';
                element.title = '';
            });
        }

        // 页面加载完成后运行坐标测试
        document.addEventListener('DOMContentLoaded', () => {
            runCoordinateTests();
        });
    </script>
</body>
</html>

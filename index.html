<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR标注工具</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
</head>
<body>
    <!-- 顶部工具栏 -->
    <header class="toolbar">
        <div class="toolbar-left">
            <span class="image-counter">
                <span id="currentImageIndex">0</span> / <span id="totalImages">0</span>
            </span>
            <span class="question-counter" id="questionCounter" title="当前图片的题目数量">
                📝 <span id="currentQuestionCount">0</span> 道题
            </span>
        </div>
        
        <div class="toolbar-center">
            <button id="prevImage" class="btn btn-nav" title="上一张 (←)">
                <i class="icon-prev"></i>
            </button>
            <div class="image-info">
                <span id="currentImageName">未选择图片</span>
                <span class="separator">|</span>
                <span id="zoomLevel">100%</span>
            </div>
            <button id="nextImage" class="btn btn-nav" title="下一张 (→)">
                <i class="icon-next"></i>
            </button>
        </div>
        
        <div class="toolbar-right">
            <!-- 工作区管理按钮 -->
            <div class="workspace-controls">
                <button id="openWorkspace" class="btn btn-primary" title="打开工作区文件夹 (Ctrl+O)">
                    <i class="icon-folder"></i>
                    <span>打开工作区</span>
                </button>
                <button id="saveCurrentToWorkspace" class="btn btn-success" title="保存当前图片到工作区 (Ctrl+S)" disabled>
                    <i class="icon-save"></i>
                    <span>保存当前</span>
                </button>
                <button id="clearAll" class="btn btn-warning" title="清空当前图片的所有标注框" disabled>
                    <i class="icon-clear-all"></i>
                    <span>清空当前</span>
                </button>
            </div>

            <div class="zoom-controls">
                <button id="zoomOut" class="btn btn-zoom" title="缩小 (-)">
                    <i class="icon-zoom-out"></i>
                </button>
                <button id="zoomReset" class="btn btn-zoom" title="重置缩放和位置 (0)">
                    <i class="icon-zoom-reset"></i>
                </button>
                <button id="zoomIn" class="btn btn-zoom" title="放大 (+)">
                    <i class="icon-zoom-in"></i>
                </button>
            </div>
            <button id="toggleAnnotations" class="btn btn-toggle active" title="显示/隐藏标注 (V)">
                <i class="icon-visibility"></i>
                <span>显示标注</span>
            </button>
            <select id="modeSelector" class="mode-selector">
                <option value="annotation">标注模式</option>
                <option value="quality-check">质检模式</option>
            </select>
            <button id="showShortcuts" class="btn btn-help" title="快捷键说明 (F1)">
                <i class="icon-help"></i>快捷键
            </button>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 左侧工具面板 -->
        <aside class="left-panel">
            <div class="panel-section">
                <h3>标注工具</h3>
                <div class="tool-buttons">
                    <button id="selectMainQuestion" class="tool-btn" data-tool="main-question" title="选择大题 (1)">
                        <i class="icon-main-question"></i>
                        <span>大题(1)</span>
                    </button>
                    <button id="selectSubQuestion" class="tool-btn" data-tool="sub-question" title="选择小题 (2)">
                        <i class="icon-sub-question"></i>
                        <span>小题(2)</span>
                    </button>
                    <button id="selectAnswerArea" class="tool-btn" data-tool="answer-area" title="选择答题区域 (3)">
                        <i class="icon-answer-area"></i>
                        <span>答题区域(3)</span>
                    </button>
                    <button id="selectImageArea" class="tool-btn" data-tool="image-area" title="选择配图区域 (4)">
                        <i class="icon-image-area"></i>
                        <span>配图区域(4)</span>
                    </button>
                    <button id="clearSelection" class="tool-btn clear-btn" title="清除选择">
                        <i class="icon-clear"></i>
                        <span>清除选择(Escape)</span>
                    </button>
                </div>
            </div>

            <div class="panel-section">
                <h3>题目导航</h3>
                <div id="questionNavigation" class="question-navigation">
                    <p class="empty-navigation">当前图片没有题目</p>
                </div>
            </div>

        </aside>

        <!-- 中央图片显示区域 -->
        <section class="image-container">
            <div class="image-wrapper" id="imageWrapper">
                <img id="currentImage" alt="当前图片">
                <canvas id="annotationCanvas"></canvas>
            </div>
        </section>

        <!-- 右侧信息面板 -->
        <aside class="right-panel">
            <div class="panel-section">
                <h3>选中区域信息</h3>
                <div id="selectedAnnotationInfo" class="annotation-info">
                    <p class="no-selection">请选择一个标注区域</p>
                </div>
            </div>

            <div class="panel-section" id="hierarchySection" style="display: none;">
                <h3 id="hierarchyTitle">层级内容</h3>
                <div id="hierarchyContent" class="hierarchy-content">
                    <p class="empty-list">请选择一个标注区域</p>
                </div>
            </div>

            <div class="panel-section quality-check-section" style="display: none;">
                <h3>质检信息</h3>
                <div id="qualityCheckInfo" class="quality-info">
                    <p>质检模式下的详细信息</p>
                </div>
            </div>
        </aside>
    </main>



    <!-- 隐藏的文件输入 -->
    <input type="file" id="imageFileInput" accept="image/*" multiple style="display: none;">
    <input type="file" id="folderInput" webkitdirectory style="display: none;">
    <input type="file" id="jsonFileInput" accept=".json" style="display: none;">

    <!-- 模态对话框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="modal-close">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/utils.js"></script>
    <script src="js/coordinate-system.js"></script>
    <script src="js/image-manager.js"></script>
    <script src="js/annotation-manager.js"></script>
    <script src="js/data-manager.js"></script>
    <script src="js/quality-check.js"></script>
    <script src="js/workspace-manager.js"></script>
    <script src="js/ui-manager.js"></script>
    <script src="js/main.js"></script>
</body>
</html>

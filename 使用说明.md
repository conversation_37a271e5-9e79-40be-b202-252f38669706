# OCR标注工具 - Electron版本使用说明

## 快速开始

### 1. 安装和启动

**方法一：使用启动脚本（推荐）**
- Windows: 双击 `start.bat`
- macOS/Linux: 运行 `./start.sh`

**方法二：命令行启动**
```bash
npm install  # 首次运行需要安装依赖
npm start    # 启动应用
```

### 2. 创建工作区

1. 启动应用后，点击"打开工作区"按钮
2. 选择一个空文件夹作为工作区
3. 应用会自动创建以下文件夹结构：
   ```
   工作区/
   ├── 1、交付json/     # JSON输出文件夹
   └── 2、原始图片/     # 图片输入文件夹
   ```

### 3. 导入图片

将待标注的图片文件复制到 `2、原始图片/` 文件夹中。
支持的格式：JPG、PNG、GIF、BMP、WebP

### 4. 开始标注

1. **选择标注工具**：
   - 按键 `1` 或点击"大题"按钮
   - 按键 `2` 或点击"小题"按钮  
   - 按键 `3` 或点击"答题区域"按钮
   - 按键 `4` 或点击"配图区域"按钮

2. **绘制标注框**：
   - 在图片上拖拽鼠标绘制矩形框
   - 松开鼠标完成标注

3. **编辑标注属性**：
   - 点击标注框选中
   - 在右侧面板编辑属性信息
   - 点击"保存"按钮确认修改

### 5. 保存数据

**保存当前图片标注**：
- 点击"保存当前到工作区"按钮
- 或使用快捷键 `Ctrl+S`

**批量保存所有标注**：
- 点击"保存所有到工作区"按钮
- 或使用快捷键 `Ctrl+Shift+S`

JSON文件会自动保存到 `1、交付json/` 文件夹，文件名格式为：`{图片名}大题{编号}.json`

## 主要功能

### 标注工具
- **大题标注**：标注整个大题区域，设置题型、题干内容等
- **小题标注**：标注小题区域，设置题干内容、印刷手写属性
- **答题区域标注**：标注学生答题区域，设置答案内容、批改结果等
- **配图区域标注**：标注题目配图区域，添加配图描述

### 数据管理
- **内存缓存**：所有标注数据在内存中缓存，切换图片时自动保存和加载
- **自动保存**：切换图片时自动保存当前标注到内存
- **批量操作**：支持批量保存所有图片的标注数据
- **数据清理**：可清除内存中的标注数据（不影响已保存的JSON文件）

### 工作区管理
- **文件夹结构**：自动创建和管理标准的文件夹结构
- **状态显示**：实时显示工作区状态、图片数量、JSON文件数量
- **文件监控**：自动检测文件夹中的图片和JSON文件

## 快捷键

### 标注工具
- `1` - 选择大题标注工具
- `2` - 选择小题标注工具
- `3` - 选择答题区域标注工具
- `4` - 选择配图区域标注工具
- `5` 或 `Esc` - 清除选择

### 图片导航
- `←` - 上一张图片
- `→` - 下一张图片

### 视图控制
- `V` - 切换标注显示/隐藏
- `+` 或 `=` - 放大图片
- `-` - 缩小图片
- `0` - 重置缩放

### 文件操作
- `Ctrl+O` - 打开工作区
- `Ctrl+S` - 保存当前标注
- `Ctrl+Shift+S` - 保存所有标注

### 其他
- `Delete` - 删除选中的标注
- `F1` - 显示快捷键帮助

## 数据格式

生成的JSON文件包含以下结构：

```json
{
  "大题1": {
    "坐标": [x1, y1, x2, y2],
    "题型": "填空题",
    "题干文字": "题目内容",
    "题目是否带配图": "否",
    "小题": {
      "小题1": {
        "坐标": [x1, y1, x2, y2],
        "题干内容": "小题内容",
        "印刷手写属性": "印刷",
        "答题区域": {
          "答题区域1": {
            "坐标": [x1, y1, x2, y2],
            "答题区域内容": "学生答案",
            "印刷手写属性": "手写",
            "批改结果": "正确",
            "正确答案": "标准答案",
            "答案解析": "解析内容"
          }
        }
      }
    },
    "配图区域坐标": {
      "配图1": [x1, y1, x2, y2]
    }
  }
}
```

## 注意事项

1. **文件权限**：确保工作区文件夹有读写权限
2. **文件编码**：JSON文件使用UTF-8编码保存
3. **数据备份**：建议定期备份工作区数据
4. **内存管理**：大量图片时建议及时保存并清理内存
5. **文件命名**：避免图片文件名包含特殊字符

## 故障排除

### 应用无法启动
- 检查Node.js版本（需要14+）
- 重新安装依赖：删除`node_modules`文件夹，运行`npm install`

### 工作区无法打开
- 检查文件夹权限
- 确保路径不包含特殊字符

### 图片无法显示
- 检查图片格式是否支持
- 确保图片文件未损坏

### JSON保存失败
- 检查`1、交付json/`文件夹权限
- 确保磁盘空间充足

如有其他问题，请检查控制台错误信息或联系技术支持。

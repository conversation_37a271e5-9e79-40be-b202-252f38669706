<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片平移功能测试</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .test-header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-toolbar {
            background: var(--primary-color);
            padding: 10px 20px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }
        .test-content {
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .test-image-container {
            width: 600px;
            height: 400px;
            border: 2px solid #ddd;
            border-radius: 4px;
            background: white;
            margin: 10px auto;
            position: relative;
            overflow: hidden;
            cursor: grab;
        }
        .test-image-container.panning {
            cursor: grabbing;
        }
        .test-image {
            width: 300px;
            height: 200px;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            position: absolute;
            top: 100px;
            left: 150px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
            transition: transform 0.1s ease;
        }
        .test-annotation {
            position: absolute;
            border: 2px dashed #e74c3c;
            background: rgba(231, 76, 60, 0.2);
            pointer-events: none;
        }
        .test-result {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
            margin: 5px;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
        }
        .test-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .status {
            text-align: center;
            margin: 10px 0;
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>图片平移功能测试</h1>
            <p>测试图片的拖拽移动功能和标注框的同步移动</p>
        </div>
        
        <div class="test-content">
            <div class="test-section">
                <h3>1. 图片平移测试</h3>
                <p>使用右键或Ctrl+左键拖拽下面的模拟图片：</p>
                
                <div class="controls">
                    <button id="resetBtn" class="btn btn-primary">重置位置</button>
                    <button id="zoomInBtn" class="btn btn-secondary">放大</button>
                    <button id="zoomOutBtn" class="btn btn-secondary">缩小</button>
                    <button id="toggleAnnotationBtn" class="btn btn-toggle active">显示标注</button>
                </div>
                
                <div id="testImageContainer" class="test-image-container">
                    <div id="testImage" class="test-image">
                        测试图片<br>
                        <small>右键或Ctrl+拖拽移动</small>
                    </div>
                    <div id="testAnnotation" class="test-annotation" style="top: 120px; left: 170px; width: 100px; height: 50px;">
                        <div style="background: #e74c3c; color: white; padding: 2px 6px; font-size: 12px; position: absolute; top: -20px; left: 0;">
                            标注1
                        </div>
                    </div>
                </div>
                
                <div class="status">
                    <div>图片位置: <span id="imagePosition">X: 0, Y: 0</span></div>
                    <div>缩放级别: <span id="zoomLevel">1.0</span></div>
                    <div>标注可见: <span id="annotationVisible">是</span></div>
                </div>
                
                <div id="testResult">
                    <span class="test-result test-info">请尝试拖拽图片...</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        class PanTest {
            constructor() {
                this.panX = 0;
                this.panY = 0;
                this.zoom = 1;
                this.isPanning = false;
                this.lastPoint = null;
                this.annotationVisible = true;
                
                this.container = document.getElementById('testImageContainer');
                this.image = document.getElementById('testImage');
                this.annotation = document.getElementById('testAnnotation');
                
                this.setupEvents();
                this.updateDisplay();
            }
            
            setupEvents() {
                // 平移事件
                this.container.addEventListener('mousedown', (e) => {
                    if (e.button === 2 || (e.button === 0 && e.ctrlKey)) {
                        e.preventDefault();
                        this.startPan(e);
                    }
                });
                
                this.container.addEventListener('mousemove', (e) => {
                    if (this.isPanning) {
                        e.preventDefault();
                        this.updatePan(e);
                    }
                });
                
                this.container.addEventListener('mouseup', () => {
                    this.stopPan();
                });
                
                this.container.addEventListener('mouseleave', () => {
                    this.stopPan();
                });
                
                this.container.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                });
                
                // 缩放事件
                this.container.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    const delta = e.deltaY > 0 ? -0.1 : 0.1;
                    this.zoom = Math.max(0.5, Math.min(3, this.zoom + delta));
                    this.updateDisplay();
                });
                
                // 按钮事件
                document.getElementById('resetBtn').addEventListener('click', () => {
                    this.reset();
                });
                
                document.getElementById('zoomInBtn').addEventListener('click', () => {
                    this.zoom = Math.min(3, this.zoom + 0.2);
                    this.updateDisplay();
                });
                
                document.getElementById('zoomOutBtn').addEventListener('click', () => {
                    this.zoom = Math.max(0.5, this.zoom - 0.2);
                    this.updateDisplay();
                });
                
                document.getElementById('toggleAnnotationBtn').addEventListener('click', () => {
                    this.toggleAnnotation();
                });
            }
            
            startPan(e) {
                this.isPanning = true;
                this.lastPoint = { x: e.clientX, y: e.clientY };
                this.container.classList.add('panning');
                document.body.classList.add('panning');
            }
            
            updatePan(e) {
                if (!this.isPanning || !this.lastPoint) return;
                
                const deltaX = e.clientX - this.lastPoint.x;
                const deltaY = e.clientY - this.lastPoint.y;
                
                this.panX += deltaX;
                this.panY += deltaY;
                
                this.lastPoint = { x: e.clientX, y: e.clientY };
                this.updateDisplay();
                
                // 更新测试结果
                document.getElementById('testResult').innerHTML = 
                    '<span class="test-result test-pass">✓ 平移功能正常工作</span>';
            }
            
            stopPan() {
                this.isPanning = false;
                this.lastPoint = null;
                this.container.classList.remove('panning');
                document.body.classList.remove('panning');
            }
            
            reset() {
                this.panX = 0;
                this.panY = 0;
                this.zoom = 1;
                this.updateDisplay();
            }
            
            toggleAnnotation() {
                this.annotationVisible = !this.annotationVisible;
                const btn = document.getElementById('toggleAnnotationBtn');
                
                if (this.annotationVisible) {
                    this.annotation.style.display = 'block';
                    btn.textContent = '隐藏标注';
                    btn.classList.add('active');
                } else {
                    this.annotation.style.display = 'none';
                    btn.textContent = '显示标注';
                    btn.classList.remove('active');
                }
                
                this.updateDisplay();
            }
            
            updateDisplay() {
                // 应用变换
                const transform = `translate(${this.panX}px, ${this.panY}px) scale(${this.zoom})`;
                this.image.style.transform = transform;
                this.annotation.style.transform = transform;
                
                // 更新状态显示
                document.getElementById('imagePosition').textContent = 
                    `X: ${Math.round(this.panX)}, Y: ${Math.round(this.panY)}`;
                document.getElementById('zoomLevel').textContent = this.zoom.toFixed(1);
                document.getElementById('annotationVisible').textContent = 
                    this.annotationVisible ? '是' : '否';
            }
        }
        
        // 启动测试
        document.addEventListener('DOMContentLoaded', () => {
            new PanTest();
        });
    </script>
</body>
</html>

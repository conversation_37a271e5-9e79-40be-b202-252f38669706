# 内存管理和批量保存功能说明

## 功能概述

OCR标注工具现已实现了更完善的内存管理和批量保存功能，提供更安全、更高效的数据管理方式。

## 主要改进

### 1. 内存保存机制优化

**保持现有优势**：
- ✅ 内存保存速度快，实时响应
- ✅ 每次标注更新都会立即保存到内存
- ✅ 防止浏览器关闭或电脑关机导致的数据丢失

**工作原理**：
- 标注数据存储在 `window.ocrTool.annotationData` Map中
- 键格式：`图片名称|其他信息`
- 值：该图片的所有标注数组
- 自动触发：创建、修改、删除标注时立即保存

### 2. 新增批量保存功能

**保存当前图片** (`保存当前` 按钮)：
- 保存当前显示图片的标注数据到JSON文件
- 按大题分别生成JSON文件
- 保持原有的单图片保存逻辑

**保存所有图片** (`保存所有` 按钮，Ctrl+S)：
- 遍历内存中所有图片的标注数据
- 为每张图片的每个大题生成独立的JSON文件
- 批量保存到工作区的JSON文件夹
- 显示保存统计信息

### 3. 内存管理功能

**清除内存** (`清除内存` 按钮)：
- 清除内存中所有图片的标注数据
- 提供确认对话框，显示将要清除的数据量
- 不影响已保存到工作区的JSON文件
- 清除后自动更新UI显示

## 界面更新

### 工具栏按钮变化

**原来**：
```
[保存到工作区] [清空所有框]
```

**现在**：
```
[保存当前] [保存所有] [清除内存] [清空当前]
```

### 按钮功能说明

| 按钮 | 功能 | 快捷键 | 启用条件 |
|------|------|--------|----------|
| 保存当前 | 保存当前图片的标注到JSON | - | 有工作区 + 当前图片有标注 |
| 保存所有 | 保存内存中所有图片的标注 | Ctrl+S | 有工作区 + 内存中有数据 |
| 清除内存 | 清除内存中的所有标注数据 | - | 总是可用 |
| 清空当前 | 清空当前图片的所有标注框 | - | 总是可用 |

## 使用场景

### 1. 日常标注工作流

```
1. 打开工作区
2. 加载图片进行标注
3. 标注会自动保存到内存
4. 定期点击"保存所有"进行持久化
5. 完成工作后再次"保存所有"确保数据安全
```

### 2. 批量处理场景

```
1. 连续标注多张图片
2. 所有标注都保存在内存中
3. 最后一次性"保存所有"到JSON文件
4. 避免频繁的文件IO操作
```

### 3. 内存清理场景

```
1. 完成一批图片的标注和保存
2. 点击"清除内存"释放内存空间
3. 开始新一批图片的标注工作
```

## 技术实现

### 数据流程

```
标注操作 → 内存保存 → 批量JSON保存 → 工作区文件
    ↓           ↓            ↓           ↓
  实时响应    防数据丢失    持久化存储   文件管理
```

### 内存结构

```javascript
window.ocrTool.annotationData = Map {
  "image1.jpg|info" => [annotation1, annotation2, ...],
  "image2.jpg|info" => [annotation3, annotation4, ...],
  ...
}
```

### 保存逻辑

**保存当前**：
```javascript
// 获取当前图片的标注
const annotations = getCurrentImageAnnotations();
// 按大题分组保存
for (const mainQuestion of mainQuestions) {
    saveJsonToWorkspace(imageName, questionNumber, jsonData);
}
```

**保存所有**：
```javascript
// 遍历内存中所有图片
for (const [imageKey, annotations] of annotationData.entries()) {
    // 为每个大题生成JSON文件
    for (const mainQuestion of mainQuestions) {
        saveJsonToWorkspace(imageName, questionNumber, jsonData);
    }
}
```

## 安全特性

### 1. 数据保护

- **实时内存保存**：防止意外关闭导致数据丢失
- **确认对话框**：清除内存前需要用户确认
- **状态提示**：清晰显示操作结果和数据量

### 2. 错误处理

- **网络异常**：保存失败时显示错误信息
- **权限问题**：文件系统访问失败时的友好提示
- **数据验证**：保存前检查数据完整性

### 3. 用户体验

- **进度反馈**：批量保存时显示处理进度
- **统计信息**：显示保存的文件数量和图片数量
- **智能按钮状态**：根据数据状态自动启用/禁用按钮

## 性能优化

### 1. 内存管理

- **按需加载**：只在内存中保存必要的标注数据
- **及时清理**：提供手动清除内存的功能
- **数据压缩**：优化标注数据的存储结构

### 2. 批量操作

- **异步处理**：批量保存使用异步操作，不阻塞UI
- **错误恢复**：单个文件保存失败不影响其他文件
- **进度跟踪**：实时显示批量操作的进度

## 使用建议

### 1. 最佳实践

- **定期保存**：建议每完成几张图片就执行一次"保存所有"
- **工作结束前保存**：离开前务必执行"保存所有"确保数据安全
- **适时清理内存**：完成一批工作后清除内存，保持系统性能

### 2. 注意事项

- **工作区必须先打开**：保存功能需要先选择工作区文件夹
- **清除内存不可恢复**：清除内存后无法恢复，请确保已保存重要数据
- **浏览器兼容性**：文件系统API需要现代浏览器支持

### 3. 故障排除

**保存按钮禁用**：
- 检查是否已打开工作区
- 确认内存中有标注数据
- 验证浏览器是否支持文件系统API

**内存数据丢失**：
- 检查浏览器是否意外关闭
- 确认是否执行了清除内存操作
- 查看工作区中是否有已保存的JSON文件

## 总结

通过这次改进，OCR标注工具实现了：

- ✅ **更安全的数据管理**：实时内存保存 + 批量持久化
- ✅ **更高效的工作流程**：支持批量处理多张图片
- ✅ **更好的内存控制**：手动清理内存，优化性能
- ✅ **更友好的用户体验**：清晰的状态反馈和操作提示

这些改进让标注工作更加安全、高效，特别适合需要处理大量图片的批量标注任务。

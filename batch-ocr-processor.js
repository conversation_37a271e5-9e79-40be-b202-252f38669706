const { ipcMain } = require('electron')

// 批量OCR处理器类
class BatchOCRProcessor {
  constructor(ocrConfig) {
    this.ocrConfig = ocrConfig
    this.batchPrompt = `我将提供多张图片（可能包含文字、公式或表格），请你逐张识别内容。
要求如下：
1. 遇到公式，请用 LaTeX 表示。
2. 遇到表格，请用 Markdown 表格格式输出。
3. 普通文本按自然段输出。
每张图片按如下格式输出：
【图片N】
<识别结果>`
  }

  /**
   * 批量处理多张图片的OCR识别
   * @param {Array} imageBoxes - 图片框数组，每个元素包含 {id, base64Image, type}
   * @param {string} provider - OCR提供商 ('doubao' 或 'qwen')
   * @param {string} modelId - 模型ID（可选）
   * @returns {Promise<Object>} 处理结果
   */
  async processBatch(imageBoxes, provider = 'doubao', modelId = null) {
    try {
      if (!imageBoxes || imageBoxes.length === 0) {
        throw new Error('没有提供图片数据')
      }

      // 限制批量处理的图片数量（避免请求过大）
      const maxBatchSize = 10
      if (imageBoxes.length > maxBatchSize) {
        throw new Error(`批量处理图片数量不能超过${maxBatchSize}张`)
      }

      console.log(`开始批量OCR处理，共${imageBoxes.length}张图片，使用${provider}`)

      // 构建批量请求的消息内容
      const messageContent = []
      
      // 添加所有图片
      imageBoxes.forEach((box, index) => {
        messageContent.push({
          type: 'image_url',
          image_url: {
            url: `data:image/png;base64,${box.base64Image}`
          }
        })
      })

      // 添加提示文本
      messageContent.push({
        type: 'text',
        text: this.batchPrompt
      })

      // 调用相应的OCR API
      const result = await this.callBatchOCR(messageContent, provider, modelId)
      
      if (!result.success) {
        throw new Error(result.error)
      }

      // 解析批量结果
      const parsedResults = this.parseBatchResults(result.data.text, imageBoxes)

      return {
        success: true,
        data: {
          results: parsedResults,
          totalTokens: result.data.tokens,
          provider: result.data.provider,
          processedCount: imageBoxes.length
        }
      }

    } catch (error) {
      console.error('批量OCR处理失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 调用批量OCR API
   * @param {Array} messageContent - 消息内容数组
   * @param {string} provider - OCR提供商
   * @param {string} modelId - 模型ID
   * @returns {Promise<Object>} API调用结果
   */
  async callBatchOCR(messageContent, provider, modelId) {
    const config = this.ocrConfig[provider]
    if (!config) {
      throw new Error(`不支持的OCR提供商: ${provider}`)
    }

    const selectedModel = modelId && config.models[modelId] ? config.models[modelId] : config.defaultModel

    try {
      const fetch = (await import('node-fetch')).default

      const requestBody = {
        model: selectedModel,
        messages: [
          {
            role: 'user',
            content: messageContent
          }
        ]
      }

      // 豆包特殊配置
      if (provider === 'doubao') {
        requestBody.thinking = { type: 'disabled' }
      }

      const response = await fetch(`${config.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.apiKey}`
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`)
      }

      const data = await response.json()
      const totalTokens = data.usage?.total_tokens || 0
      
      console.log(`${provider}批量OCR total_tokens:`, totalTokens)

      if (data.choices && data.choices[0] && data.choices[0].message) {
        return {
          success: true,
          data: {
            text: data.choices[0].message.content,
            tokens: totalTokens,
            provider: provider
          }
        }
      } else {
        throw new Error('API响应格式错误')
      }

    } catch (error) {
      console.error(`${provider}批量OCR调用失败:`, error)
      return {
        success: false,
        error: `批量OCR识别失败: ${error.message}`
      }
    }
  }

  /**
   * 解析批量OCR结果
   * @param {string} batchText - 批量OCR返回的文本
   * @param {Array} imageBoxes - 原始图片框数组
   * @returns {Array} 解析后的结果数组
   */
  parseBatchResults(batchText, imageBoxes) {
    const results = []
    
    try {
      // 使用正则表达式匹配【图片N】格式
      const imagePattern = /【图片(\d+)】\s*([\s\S]*?)(?=【图片\d+】|$)/g
      const matches = [...batchText.matchAll(imagePattern)]

      // 如果没有匹配到标准格式，尝试按行分割
      if (matches.length === 0) {
        console.warn('未找到标准格式，尝试按行分割结果')
        const lines = batchText.split('\n').filter(line => line.trim())
        
        imageBoxes.forEach((box, index) => {
          results.push({
            id: box.id,
            type: box.type,
            text: lines[index] || '',
            confidence: 0.8 // 默认置信度
          })
        })
      } else {
        // 按标准格式解析
        imageBoxes.forEach((box, index) => {
          const imageNumber = index + 1
          const match = matches.find(m => parseInt(m[1]) === imageNumber)
          
          results.push({
            id: box.id,
            type: box.type,
            text: match ? match[2].trim() : '',
            confidence: match ? 0.95 : 0.0
          })
        })
      }

      // 确保结果数量与输入图片数量一致
      while (results.length < imageBoxes.length) {
        const missingIndex = results.length
        results.push({
          id: imageBoxes[missingIndex].id,
          type: imageBoxes[missingIndex].type,
          text: '',
          confidence: 0.0
        })
      }

    } catch (error) {
      console.error('解析批量结果失败:', error)
      
      // 返回空结果
      imageBoxes.forEach(box => {
        results.push({
          id: box.id,
          type: box.type,
          text: '',
          confidence: 0.0,
          error: '解析失败'
        })
      })
    }

    return results
  }

  /**
   * 分组处理大量图片（自动分批）
   * @param {Array} imageBoxes - 所有图片框
   * @param {string} provider - OCR提供商
   * @param {string} modelId - 模型ID
   * @param {number} batchSize - 每批处理的图片数量
   * @returns {Promise<Object>} 处理结果
   */
  async processInBatches(imageBoxes, provider = 'doubao', modelId = null, batchSize = 8) {
    try {
      const allResults = []
      let totalTokens = 0
      const batches = []

      // 分批
      for (let i = 0; i < imageBoxes.length; i += batchSize) {
        batches.push(imageBoxes.slice(i, i + batchSize))
      }

      console.log(`总共${imageBoxes.length}张图片，分为${batches.length}批处理`)

      // 逐批处理
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i]
        console.log(`处理第${i + 1}/${batches.length}批，包含${batch.length}张图片`)

        const batchResult = await this.processBatch(batch, provider, modelId)
        
        if (batchResult.success) {
          allResults.push(...batchResult.data.results)
          totalTokens += batchResult.data.totalTokens
        } else {
          // 批次失败时，为该批次的所有图片添加错误结果
          batch.forEach(box => {
            allResults.push({
              id: box.id,
              type: box.type,
              text: '',
              confidence: 0.0,
              error: batchResult.error
            })
          })
        }

        // 批次间延迟，避免API限流
        if (i < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      return {
        success: true,
        data: {
          results: allResults,
          totalTokens: totalTokens,
          provider: provider,
          processedCount: imageBoxes.length,
          batchCount: batches.length
        }
      }

    } catch (error) {
      console.error('分批处理失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
}

module.exports = BatchOCRProcessor

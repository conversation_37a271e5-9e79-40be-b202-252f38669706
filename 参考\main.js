const { app, BrowserWindow, Menu, dialog, ipcMain } = require('electron')
const path = require('path')
const fs = require('fs')
const isDev = process.env.NODE_ENV === 'development'

// OCR API配置（安全存储在主进程中）
const OCR_CONFIG = {
  doubao: {
    baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
    apiKey: 'ba6f3697-0fe3-4651-b6c9-e9e12325c17d',
    models: {
      'doubao-seed-1-6-flash-250715': 'doubao-seed-1-6-flash-250715',
      'doubao-seed-1-6-250615': 'doubao-seed-1-6-250615',
      'doubao-1-5-thinking-vision-pro-250428': 'doubao-1-5-thinking-vision-pro-250428'
    },
    defaultModel: 'doubao-1-5-thinking-vision-pro-250428'
  },
  qwen: {
    baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    apiKey: 'sk-001b3a82e31a4bf39aedea1c3a285301',
    models: {
      'qwen-vl-ocr-latest': 'qwen-vl-ocr-latest',
      'qwen-vl-max-latest': 'qwen-vl-max-latest',
      'qwen-vl-plus-latest': 'qwen-vl-plus-latest'
    },
    defaultModel: 'qwen-vl-plus-latest'
  }
}

const OCR_PROMPT = "请你作为一个OCR识别工具，只提取图像中的文字内容，不进行解释或解答。具体要求如下：1. 普通文本保持原有格式，包括换行；2. 遇到数学公式时，请使用 LaTeX 语言准确表达，使用$包裹公式；3. 遇到表格时，请使用 Markdown 表格语法准确还原；4. 不要输出任何题目解析、推理或答案；5. 只输出识别内容，不添加任何多余说明。"

// 导入授权验证模块
const LicenseManager = require('./license/licenseManager')
const licenseManager = new LicenseManager()

// 保持对窗口对象的全局引用
let mainWindow

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: false, // 允许加载本地文件
      preload: path.join(__dirname, 'preload.js'),
      devTools: isDev // 只在开发环境启用开发者工具
    },
    icon: path.join(__dirname, '../public/favicon.ico'),
    show: false, // 先不显示，等加载完成后再显示
    titleBarStyle: 'default'
  })

  // 禁用生产环境的开发者工具
  if (!isDev) {
    // 禁用右键菜单中的"检查元素"
    mainWindow.webContents.on('context-menu', (e) => {
      e.preventDefault()
    })

    // 禁用所有开发者工具快捷键
    mainWindow.webContents.on('before-input-event', (event, input) => {
      // 禁用 F12
      if (input.key === 'F12') {
        event.preventDefault()
      }
      // 禁用 Ctrl+Shift+I (Windows/Linux)
      if (input.control && input.shift && input.key === 'I') {
        event.preventDefault()
      }
      // 禁用 Cmd+Option+I (macOS)
      if (input.meta && input.alt && input.key === 'I') {
        event.preventDefault()
      }
      // 禁用 Ctrl+Shift+J (Windows/Linux)
      if (input.control && input.shift && input.key === 'J') {
        event.preventDefault()
      }
      // 禁用 Cmd+Option+J (macOS)
      if (input.meta && input.alt && input.key === 'J') {
        event.preventDefault()
      }
      // 禁用 Ctrl+U (查看源代码)
      if (input.control && input.key === 'U') {
        event.preventDefault()
      }
      // 禁用 Cmd+U (macOS查看源代码)
      if (input.meta && input.key === 'U') {
        event.preventDefault()
      }
    })
  }

  // 加载应用
  if (isDev) {
    // 开发环境
    mainWindow.loadURL('http://localhost:5173')
    // 打开开发者工具
    mainWindow.webContents.openDevTools()
  } else {
    // 生产环境
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
  }

  // 窗口加载完成后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()

    // 如果是开发环境，最大化窗口
    if (isDev) {
      mainWindow.maximize()
    }
  })

  // 生产环境额外安全措施
  if (!isDev) {
    // 禁用新窗口创建（防止通过window.open打开开发者工具）
    mainWindow.webContents.setWindowOpenHandler(() => {
      return { action: 'deny' }
    })

    // 监听并阻止开发者工具相关的导航
    mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
      const url = new URL(navigationUrl)
      if (url.protocol === 'devtools:') {
        event.preventDefault()
      }
    })

    // 阻止开发者工具的打开
    mainWindow.webContents.on('devtools-opened', () => {
      mainWindow.webContents.closeDevTools()
    })
  }

  // 当窗口被关闭时
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // 设置菜单
  createMenu()
}

function createMenu() {
  // 视图菜单项，根据环境动态生成
  const viewSubmenu = [
    { label: '重新加载', accelerator: 'CmdOrCtrl+R', role: 'reload' },
    { label: '强制重新加载', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' }
  ]

  // 只在开发环境添加开发者工具
  if (isDev) {
    viewSubmenu.push({ label: '开发者工具', accelerator: 'F12', role: 'toggleDevTools' })
  }

  viewSubmenu.push(
    { type: 'separator' },
    { label: '实际大小', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
    { label: '放大', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
    { label: '缩小', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
    { type: 'separator' },
    { label: '全屏', accelerator: 'F11', role: 'togglefullscreen' }
  )

  const template = [
    {
      label: '文件',
      submenu: [
        {
          label: '新建项目',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-new-project')
          }
        },
        {
          label: '打开项目',
          accelerator: 'CmdOrCtrl+O',
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openDirectory'],
              title: '选择项目文件夹'
            })

            if (!result.canceled && result.filePaths.length > 0) {
              mainWindow.webContents.send('menu-open-project', result.filePaths[0])
            }
          }
        },
        {
          label: '保存',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            mainWindow.webContents.send('menu-save')
          }
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        { label: '撤销', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: '重做', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: '剪切', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: '复制', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: '粘贴', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: '视图',
      submenu: viewSubmenu
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于',
              message: 'OCR题目标注工具',
              detail: '版本: 1.0.0\n基于Electron和Vue3构建'
            })
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// 当所有窗口都被关闭时退出应用
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow()
  }
})

// 应用准备就绪时创建窗口
app.whenReady().then(async () => {
  // 初始化许可证管理器
  const initResult = await licenseManager.initialize()

  if (!initResult.success) {
    console.error('许可证管理器初始化失败:', initResult.error)
  }

  createWindow()
})

// 处理文件对话框
ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options)
  return result
})

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options)
  return result
})

// 许可证相关IPC处理
ipcMain.handle('license-check-status', async () => {
  return licenseManager.checkLicenseStatus()
})

ipcMain.handle('license-activate', async (event, licenseCode) => {
  return await licenseManager.activateLicense(licenseCode)
})

ipcMain.handle('license-get-details', async () => {
  return licenseManager.getLicenseDetails()
})

ipcMain.handle('license-generate-trial', async () => {
  return await licenseManager.generateTrialLicense()
})

ipcMain.handle('license-reset', async () => {
  return await licenseManager.resetLicense()
})

ipcMain.handle('license-get-device-info', async () => {
  return licenseManager.getDeviceInfo()
})

ipcMain.handle('license-can-run', async () => {
  return licenseManager.canRunApplication()
})

ipcMain.handle('license-save-api-config', async (event, config) => {
  return await licenseManager.saveApiConfig(config)
})

ipcMain.handle('license-load-api-config', async () => {
  return await licenseManager.loadApiConfig()
})

ipcMain.handle('license-get-storage-stats', async () => {
  return licenseManager.getStorageStats()
})

// 图片保存相关IPC处理
ipcMain.handle('image-select-save-directory', async () => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openDirectory'],
      title: '选择图片保存目录'
    })

    if (result.canceled) {
      return { canceled: true }
    }

    const selectedPath = result.filePaths[0]

    // 检查目录是否存在和可写
    try {
      await fs.promises.access(selectedPath, fs.constants.W_OK)
      return {
        success: true,
        path: selectedPath,
        canceled: false
      }
    } catch (error) {
      return {
        success: false,
        error: '选择的目录不可写入',
        canceled: false
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      canceled: false
    }
  }
})

ipcMain.handle('image-save-to-file', async (event, base64Data, filePath) => {
  try {
    // 验证参数
    if (!base64Data || !filePath) {
      return {
        success: false,
        error: '缺少必要参数'
      }
    }

    // 提取base64数据（去掉data:image/xxx;base64,前缀）
    let imageData = base64Data
    if (base64Data.startsWith('data:')) {
      const base64Index = base64Data.indexOf(',')
      if (base64Index !== -1) {
        imageData = base64Data.substring(base64Index + 1)
      }
    }

    // 将base64转换为Buffer
    const buffer = Buffer.from(imageData, 'base64')

    // 确保目录存在
    const dir = path.dirname(filePath)
    try {
      await fs.promises.access(dir)
    } catch {
      await fs.promises.mkdir(dir, { recursive: true })
    }

    // 写入文件
    await fs.promises.writeFile(filePath, buffer)

    return {
      success: true,
      filePath: filePath
    }
  } catch (error) {
    console.error('保存图片失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
})

ipcMain.handle('image-check-directory-exists', async (event, dirPath) => {
  try {
    await fs.promises.access(dirPath, fs.constants.F_OK)
    return { exists: true }
  } catch {
    return { exists: false }
  }
})

ipcMain.handle('image-save-json-file', async (event, { content, filename, directory, mimeType }) => {
  try {
    // 验证参数
    if (!content || !filename || !directory) {
      return {
        success: false,
        error: '缺少必要参数'
      }
    }

    // 构建完整文件路径
    const filePath = path.join(directory, filename)

    // 确保目录存在
    try {
      await fs.promises.access(directory)
    } catch {
      await fs.promises.mkdir(directory, { recursive: true })
    }

    // 写入文件
    await fs.promises.writeFile(filePath, content, 'utf8')

    return {
      success: true,
      filePath: filePath
    }
  } catch (error) {
    console.error('保存JSON文件失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
})

ipcMain.handle('image-delete-file', async (event, filePath) => {
  try {
    // 验证参数
    if (!filePath) {
      return {
        success: false,
        error: '文件路径不能为空'
      }
    }

    // 检查文件是否存在
    try {
      await fs.promises.access(filePath, fs.constants.F_OK)
    } catch {
      // 文件不存在，认为删除成功
      return {
        success: true,
        message: '文件不存在，无需删除'
      }
    }

    // 删除文件
    await fs.promises.unlink(filePath)

    return {
      success: true,
      filePath: filePath
    }
  } catch (error) {
    console.error('删除文件失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
})

// OCR API处理
ipcMain.handle('ocr-call-doubao', async (event, base64Image, modelId = null) => {
  const config = OCR_CONFIG.doubao

  // 使用指定的模型ID，如果没有指定则使用默认模型
  const selectedModel = modelId && config.models[modelId] ? config.models[modelId] : config.defaultModel

  try {
    const fetch = (await import('node-fetch')).default

    const response = await fetch(`${config.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify({
        model: selectedModel,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'image_url',
                image_url: {
                  url: `data:image/png;base64,${base64Image}`
                }
              },
              {
                type: 'text',
                text: OCR_PROMPT
              }
            ]
          }
        ],
        thinking: {
          type: 'disabled'
        }
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`)
    }

    const data = await response.json()
    const total_token = data.usage?.total_tokens || 0
    console.log('豆包OCR total_token:', total_token)

    if (data.choices && data.choices[0] && data.choices[0].message) {
      return {
        success: true,
        data: {
          text: data.choices[0].message.content,
          tokens: total_token,
          provider: 'doubao'
        }
      }
    } else {
      throw new Error('API响应格式错误')
    }
  } catch (error) {
    console.error('豆包OCR调用失败:', error)
    return {
      success: false,
      error: `OCR识别失败: ${error.message}`
    }
  }
})

ipcMain.handle('ocr-call-aliyun', async (event, base64Image, modelId = null) => {
  const config = OCR_CONFIG.qwen

  // 使用指定的模型ID，如果没有指定则使用默认模型
  const selectedModel = modelId && config.models[modelId] ? config.models[modelId] : config.defaultModel

  try {
    const fetch = (await import('node-fetch')).default

    const response = await fetch(`${config.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify({
        model: selectedModel,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'image_url',
                image_url: {
                  url: `data:image/png;base64,${base64Image}`
                }
              },
              {
                type: 'text',
                text: OCR_PROMPT
              }
            ]
          }
        ]
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`)
    }

    const data = await response.json()
    const total_token = data.usage?.total_tokens || 0
    console.log('阿里云OCR total_token:', total_token)

    if (data.choices && data.choices[0] && data.choices[0].message) {
      return {
        success: true,
        data: {
          text: data.choices[0].message.content,
          tokens: total_token,
          provider: 'aliyun'
        }
      }
    } else {
      throw new Error('API响应格式错误')
    }
  } catch (error) {
    console.error('阿里云OCR调用失败:', error)
    return {
      success: false,
      error: `OCR识别失败: ${error.message}`
    }
  }
})
